<template>
  <div ref="propertyPanel" class="property-panel">
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item name="1">
        <template slot="title">
          <span class="el_title">基本设置<i class="header-icon el-icon-info"/></span>
        </template>
        <el-form :model="form" label-width="100px" size="small">
          <el-form-item label="标识">
            <el-input v-model="form.id" placeholder="请输入节点标识" :readonly="rootElement ? true : false"
                      class="el_input" @input="updateKey"/>
          </el-form-item>
          <el-form-item label="名称" v-show="!rootElement">
            <el-input v-model="form.name" :readonly="rootElement ? true : false" placeholder="请输入名称"
                      class="el_input" @input="updateName"/>
          </el-form-item>
          <!--          <div v-if="rootElement">-->
          <!--            <el-form-item label="描述">-->
          <!--              <el-input v-model="form.description" :readonly="rootElement ? true : false" :rows="3" type="textarea" clearable placeholder="请输入描述" class="el_input" @input="updateDocumentation" />-->
          <!--            </el-form-item>-->
          <!--          </div>-->
        </el-form>
      </el-collapse-item>
      <!--      <el-collapse-item v-if="isUserTask">-->
      <!--        <template slot="title">-->
      <!--          <span class="el_title">审批人<i class="header-icon el-icon-info" /></span>-->
      <!--        </template>-->
      <!--        <div class="el_title">-->
      <!--          <el-button type="primary" size="mini" @click="addApprovers()">选择</el-button>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.choose_user.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>用户</span>-->
      <!--            </div>-->
      <!--            <span v-for="(o, index) in approversData.choose_user" :key="o.code">-->
      <!--              <span v-if="o.code === 'creator_self'">【{{ o.name }}】</span>-->
      <!--              <span v-if="o.code === 'submit_self'">【{{ o.name }}】</span>-->
      <!--              <span v-else>{{ o.name }}</span>-->
      <!--              <span v-if="index < approversData.choose_user.length - 1">,</span>-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.choose_post.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>岗位</span>-->
      <!--            </div>-->
      <!--            <span v-for="o in approversData.choose_post" :key="o.code">-->
      <!--              【{{ o.name }}】-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.process_variables.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>流程变量</span>-->
      <!--            </div>-->
      <!--            <span v-for="o in approversData.process_variables" :key="o.code">-->
      <!--              【{{ o.name }}】-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.creator_self.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>流程发起人</span>-->
      <!--            </div>-->
      <!--            <span v-for="o in approversData.creator_self" :key="o.code">-->
      <!--              【{{ o.name }}】-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.submit_self.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>流程发起人(审批)</span>-->
      <!--            </div>-->
      <!--            <span v-for="o in approversData.submit_self" :key="o.code">-->
      <!--              【{{ o.name }}】-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.creator_superior.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>发起人直接上级</span>-->
      <!--            </div>-->
      <!--            <span v-for="o in approversData.creator_superior" :key="o.code">-->
      <!--              【{{ o.name }}】-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.node_superior.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>发起人直接上级</span>-->
      <!--            </div>-->
      <!--            <span v-for="o in approversData.node_superior" :key="o.code">-->
      <!--              【{{ o.name }}】-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="approversData.choose_dept.length > 0">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>部门</span>-->
      <!--            </div>-->
      <!--            <span v-for="o in approversData.choose_dept" :key="o.code">-->
      <!--              【{{ o.name }}】-->
      <!--            </span>-->
      <!--          </el-card>-->
      <!--        </div>-->
      <!--      </el-collapse-item>-->
      <!--      <el-collapse-item v-if="isStart || isTask">-->
      <!--        <template slot="title">-->
      <!--          <span class="el_title">表单设置<i class="header-icon el-icon-info" /></span>-->
      <!--        </template>-->
      <!--        <div class="el_title">-->
      <!--          <el-button type="primary" size="mini" @click="addCustomForms()">选择</el-button>-->
      <!--          <el-card shadow="never" style="margin-top: 10px" v-show="formFieldsData.formId">-->
      <!--            <div slot="header" class="clearfix">-->
      <!--              <span>{{ formFieldsData.formName }}({{-->
      <!--                  formFieldsData.formKey-->
      <!--                }})可编辑字段：</span>-->
      <!--            </div>-->
      <!--            <div>-->
      <!--              {{ fieldStr }}-->
      <!--            </div>-->
      <!--          </el-card>-->
      <!--        </div>-->
      <!--      </el-collapse-item>-->
      <!--      <el-collapse-item v-if="isUserTask">-->
      <!--        <template slot="title">-->
      <!--          <span class="el_title">按钮配置<i class="header-icon el-icon-info" /></span>-->
      <!--        </template>-->
      <!--        <div class="el_title">-->
      <!--          <el-button type="primary" size="mini" @click="addButton()">选择</el-button>-->
      <!--          <el-table v-if="buttonsData.length > 0" :data="buttonsData" border style="width: 90%; margin-top: 5px">-->
      <!--            <el-table-column prop="name" label="名称" align="center" />-->
      <!--            <el-table-column label="操作" align="center" width="100px">-->
      <!--              <template v-slot="scope">-->
      <!--                <el-button size="mini" type="text" @click="removeButton(scope.row)">删除</el-button>-->
      <!--              </template>-->
      <!--            </el-table-column>-->
      <!--          </el-table>-->
      <!--        </div>-->
      <!--      </el-collapse-item>-->
      <!--      <el-collapse-item v-if="isUserTask">-->
      <!--        <template slot="title">-->
      <!--          <span class="el_title">会签配置<i class="header-icon el-icon-info" /></span>-->
      <!--        </template>-->
      <!--        <div class="el_title">-->
      <!--          <el-form :model="form" label-width="100px" size="small">-->
      <!--            <el-form-item label="会签类型">-->
      <!--              <el-select style="width: 247px" v-model="multiProp.multiUserTaskFlag" clearable placeholder="请选择" class="el_input" @change="multiInstance">-->
      <!--                <el-option v-for="item in multiUserTaskFlagList" :key="item.value" :label="item.label" :value="item.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="完成条件" v-show="-->
      <!--                multiProp.multiUserTaskFlag && multiProp.multiUserTaskFlag != 8-->
      <!--              ">-->
      <!--              <el-select style="width: 247px" v-model="multiProp.multiUserTaskWay" clearable placeholder="请选择" class="el_input" @change="multiInstance">-->
      <!--                <el-option v-for="item in multiUserTaskWayList" :key="item.value" :label="item.label" :value="item.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="百分比" v-show="-->
      <!--                multiProp.multiUserTaskFlag &&-->
      <!--                multiProp.multiUserTaskFlag != 8 &&-->
      <!--                multiProp.multiUserTaskWay &&-->
      <!--                multiProp.multiUserTaskWay == 'percent'-->
      <!--              ">-->
      <!--              <el-input-number style="width: 247px" :min="10" :max="100" :step="10" :step-strictly="true" @change="multiInstance" v-model="multiProp.multiUserTaskPercentValue" placeholder="请输入百分比"-->
      <!--                class="el_input" />-->
      <!--            </el-form-item>-->
      <!--          </el-form>-->
      <!--        </div>-->
      <!--      </el-collapse-item>-->
      <el-collapse-item v-if="isExclusiveSequenceFlow">
        <template slot="title">
          <span class="el_title">分支条件<i class="header-icon el-icon-info"/></span>
        </template>
        <el-form ref="userTaskForm" :model="exclusiveSequence" label-width="100px" size="small">
          <el-form-item label="流程表达式">
            <el-input type="textarea" :autosize="{ minRows: 10, maxRows: 50}"
                      v-model="exclusiveSequence.conditionExpression" clearable placeholder="请选择流程表达式"
                      class="el_input"
                      @input="updateConditionExpression">
              <el-button slot="append" icon="el-icon-plus"/>
            </el-input>
            <el-button size="mini" type="primary" @click="ruleConfig()">增加条件</el-button>
          </el-form-item>
        </el-form>
      </el-collapse-item>
      <!--      <el-collapse-item v-if="!isGateway">-->
      <!--        <template slot="title">-->
      <!--          <span class="el_title">执行监听<i class="header-icon el-icon-info" /></span>-->
      <!--        </template>-->
      <!--        <div class="el_title">-->
      <!--          <el-button type="primary" size="mini" @click="addListeners(false)" v-if="isShowButton">添加</el-button>-->
      <!--          <el-table v-if="listenerData.length > 0" :data="listenerData" border style="width: 98%; margin-top: 5px">-->
      <!--            <el-table-column prop="eventType" label="事件" align="center" />-->
      <!--            <el-table-column prop="listenerType" label="类型" align="center">-->
      <!--              <template v-slot="scope">-->
      <!--                <span>{{ listenerType[scope.row.listenerType] }}</span>-->
      <!--              </template>-->
      <!--            </el-table-column>-->
      <!--            <el-table-column prop="value" label="实现" align="center" />-->
      <!--            <el-table-column label="操作" align="center">-->
      <!--              <template v-slot="scope">-->
      <!--                <el-button size="mini" type="text" @click="updateListener(scope.row, false)">修改</el-button>-->
      <!--                <el-button size="mini" type="text" @click="listenerDel(scope.row, false)">删除</el-button>-->
      <!--              </template>-->
      <!--            </el-table-column>-->
      <!--          </el-table>-->

      <!--        </div>-->
      <!--      </el-collapse-item>-->
      <el-collapse-item v-if="isServiceTask">
        <template slot="title">
          <span class="el_title">规则配置<i class="header-icon el-icon-info"/></span>
        </template>
        <div class="el_title">
          <!--          <el-button type="primary" size="mini" @click="addServiceTask" v-if="!flowService.value">添加</el-button>-->
          <el-button size="mini" type="primary" @click="businessRuleConfig()">设置条件</el-button>
          <el-card shadow="never" style="margin-top: 10px" v-if="flowService.value">
            <div slot="header" class="clearfix">
              <span>类型:</span>
              <span>{{ flowService.listenerType === 'class' ? '类' : '表达式' }}</span>
            </div>
            <div class="clearfix">
<!--              <el-row>-->
<!--                <el-col :span="24">-->
                  <span>值:</span>
                  <span>
              【{{ flowService.value }}】
            </span>
<!--                </el-col>-->
<!--                <el-col :span="4">-->
<!--                  <el-button class="right" size="mini" type="danger" @click="serviceTaskDel()">删除1</el-button>-->
<!--                </el-col>-->
<!--              </el-row>-->
            </div>
          </el-card>
        </div>
      </el-collapse-item>
      <!--      <el-collapse-item v-if="isUserTask">-->
      <!--        <template slot="title">-->
      <!--          <span class="el_title">任务监听<i class="header-icon el-icon-info" /></span>-->
      <!--        </template>-->
      <!--        <div class="el_title">-->
      <!--          <el-button type="primary" size="mini" @click="addListeners(true)" v-if="isShowButtonTask">添加</el-button>-->
      <!--          <el-table v-if="taskListenerData.length > 0" :data="taskListenerData" border style="width: 98%; margin-top: 5px">-->
      <!--            <el-table-column prop="eventType" label="事件" align="center" />-->
      <!--            <el-table-column prop="listenerType" label="类型" align="center">-->
      <!--              <template v-slot="scope">-->
      <!--                <span>{{ listenerType[scope.row.listenerType] }}</span>-->
      <!--              </template>-->
      <!--            </el-table-column>-->
      <!--            <el-table-column prop="value" label="实现" align="center" />-->
      <!--            <el-table-column label="操作" align="center">-->
      <!--              <template v-slot="scope">-->
      <!--                <el-button size="mini" type="text" @click="updateListener(scope.row, true)">修改</el-button>-->
      <!--                <el-button size="mini" type="text" @click="listenerDel(scope.row, true)">删除</el-button>-->
      <!--              </template>-->
      <!--            </el-table-column>-->
      <!--          </el-table>-->
      <!--        </div>-->
      <!--      </el-collapse-item>-->
      <!--      <el-collapse-item v-if="isUserTask">-->
      <!--        <template slot="title">-->
      <!--          <span class="el_title">其他设置<i class="header-icon el-icon-info" /></span>-->
      <!--        </template>-->
      <!--        <div class="el_title">-->
      <!--          <el-form :model="form" label-width="100px" size="small">-->
      <!--            <el-form-item label="跳过策略" v-show="skipShowFlag">-->
      <!--              <el-select style="width: 247px" v-model="otherProperties.skip" multiple clearable placeholder="请选择" class="el_input" @change="skipChange">-->
      <!--                <el-option v-for="item in skipList" :key="item.value" :label="item.label" :value="item.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="超时策略">-->
      <!--              <el-select style="width: 247px" v-model="otherProperties.overTime" multiple clearable placeholder="请选择" class="el_input" @change="overTimeChange">-->
      <!--                <el-option v-for="item in overTimeList" :key="item.value" :label="item.label" :value="item.value">-->
      <!--                </el-option>-->
      <!--              </el-select>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="超时时间" v-if="isOverTime">-->
      <!--              <el-input-number v-model="otherProperties.overTimeValue" @change="overTimeChange" :min="1" :max="72" label="超时时间"></el-input-number>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="调用类型" v-if="isShowUrlType">-->
      <!--              <el-radio v-model="otherProperties.urlType" @change="overTimeChange" label="1">调用微服务内部接口-->
      <!--                <span class="span-box">-->
      <!--                  <el-tooltip class="item" effect="dark" content="格式: 服务名/具体url地址  例: /process/api/out/select_task_todo" placement="top">-->
      <!--                    <i class="el-icon-question"></i>-->
      <!--                  </el-tooltip>-->
      <!--                </span>-->
      <!--              </el-radio>-->
      <!--              <el-radio v-model="otherProperties.urlType" @change="overTimeChange" label="0">调用第三方接口-->
      <!--                <span class="span-box">-->
      <!--                  <el-tooltip class="item" effect="dark" content="例: ***********:88/out/select_task_todo" placement="top">-->
      <!--                    <i class="el-icon-question"></i>-->
      <!--                  </el-tooltip>-->
      <!--                </span>-->
      <!--              </el-radio>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="远程地址" v-if="isShowRemoteUrl">-->
      <!--              <el-input type="textarea" v-model="otherProperties.remoteUrl" @blur="overTimeChange"></el-input>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="节点级别" >-->
      <!--                <el-input-number v-model="otherProperties.nodeLevel" :step="10" @change="nodeLevelChange"></el-input-number>-->
      <!--            </el-form-item>-->
      <!--          </el-form>-->
      <!--        </div>-->
      <!--      </el-collapse-item>-->
    </el-collapse>
    <!--选择审批对象-->
    <ApproveSelect ref="ApproveSelect" @callback="handleCallback"/>
    <!--选择表单-->
    <CustomFormSelect ref="CustomFormSelect" @callback="handleCustomFormCallback"/>
    <!--执行监听器-->
    <flow-listener ref="FlowListener" @callback="handleListenerCallback" :elementData="listenerData"
                   :sequenceFlow="sequenceFlow"/>
    <!--服务任务-->
    <flow-service ref="FlowService" @callback="handleServiceCallback" :elementData="listenerData"
                  :sequenceFlow="sequenceFlow"/>
    <rule-config ref="RuleConfig" @callback="handleRuleConfigCallback"/>
    <business-rule-config ref="BusinessRuleConfig" @callback="handleBusinessRuleConfigCallback"/>
    <!--任务监听器-->
    <flow-task-listener ref="FlowTaskListener" @callback="handleListenerCallback" :elementData="taskListenerData"
                        :sequenceFlow="sequenceFlow"/>
    <!--按钮-->
    <flow-button ref="FlowButton" @callback="handleButtonCallback"/>
  </div>
</template>
<script>
import cmdHelper from './CustomPanel/lib/helper/CmdHelper'
import elementHelper from './CustomPanel/lib/helper/ElementHelper'
import extensionElementsHelper from './CustomPanel/lib/helper/ExtensionElementsHelper'
import ImplementationTypeHelper from './CustomPanel/lib/helper/ImplementationTypeHelper'
import ApproveSelect from './ApproveSelect.vue'
import CustomFormSelect from './CustomFormSelect.vue'
import RuleConfig from './RuleConfig.vue'
import BusinessRuleConfig from './BusinessRuleConfig.vue'
import FlowListener from './FlowListener.vue'
import FlowTaskListener from './FlowTaskListener.vue'
import FlowButton from './FlowButton.vue'
import FlowService from './FlowService.vue'
import { randomString } from '@/views/common/util'
import util from '@/libs/util'

export default {
  name: 'PropertyPanel',
  components: {
    BusinessRuleConfig,
    ApproveSelect,
    FlowListener,
    FlowTaskListener,
    FlowButton,
    FlowService,
    RuleConfig,
    CustomFormSelect
  },
  props: {
    modeler: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isOverTime: false,
      isShowButton: true,
      isShowButtonTask: true,
      isShowUrlType: false,
      // 跳过节点配置的显示隐藏标志
      skipShowFlag: true,
      // 远程地址输入文本域显示
      isShowRemoteUrl: false,
      // 字段数据展示字符串
      fieldStr: '',
      // 所属业务ID
      bussinessId: '',
      selectedElements: [],
      element: {},
      rootElement: null,
      activeName: '1',
      // 选中的监听器数组
      listenerData: [],
      serviceTaskData: {},
      // 监听器
      listener: {},
      // 选中的任务监听器数组
      taskListenerData: [],
      // 表单字段
      formFieldsData: {
        formName: '',
        formId: '',
        formKey: '',
        formUrl: '',
        fieldList: []
      },
      // 表单字段（暂存）
      formFieldsTemp: {},
      // 任务监听器
      taskListener: {},
      isTaskListener: false,
      listenerType: {
        class: '类',
        expression: '表达式',
        delegateExpression: '代理表达式'
      },
      // 按钮
      buttonsData: [],
      // 按钮（暂存）
      buttonsTemp: {},
      // 审批对象
      approversData: {
        choose_user: [],
        choose_post: [],
        creator_self: [],
        submit_self: [],
        creator_superior: [],
        node_superior: [],
        process_variables: [],
        choose_dept: []
      },
      // 审批对象（暂存）
      approversTemp: {},
      form: {
        id: '',
        name: '',
        versionTag: '',
        taskPriority: '',
        jobPriority: '',
        candidateStarterGroups: '',
        candidateStarterUsersNames: '',
        candidateStarterUsers: '',
        historyTimeToLive: '',
        // 流程发起人
        initiator: '',
        description: ''

      },
      flowService: {},
      multiProp: {
        // 会签设置(8不是会签，1是并行会签，2是串行会签)
        multiUserTaskFlag: 8,
        // 会签百分比还是全票 all是全票，percent是百分比
        multiUserTaskWay: 'all',
        // 会签百分比数值
        multiUserTaskPercentValue: 100
      },
      multiPropTemp: {},
      //  网关条件
      exclusiveSequence: {
        conditionExpression: '',
        expressionData: '',
        value: ''
      },
      //  跳过策略（暂存）
      skipValuesTemp: {},
      //  节点级别策略（暂存）
      nodeLevelValuesTemp: {},
      //  超时时间（暂存）
      overTimeValuesTemp: {},
      //  超时url（暂存）
      overTimeUrlTemp: {},
      //  超时urlType（暂存）
      overTimeUrlTypeTemp: {},
      //  超时策略（暂存）
      overTimeTemp: {},
      //  网关条件（暂存）
      exclusiveSequenceTemp: {},
      multiUserTaskWayList: [
        {
          label: '全票通过',
          value: 'all'
        },
        {
          label: '百分比',
          value: 'percent'
        }
      ],

      multiUserTaskFlagList: [
        {
          label: '无',
          value: 8
        },
        {
          label: '并行会签',
          value: 1
        },
        {
          label: '串行会签',
          value: 2
        }
      ],
      // 超时策略元数据数组
      overTimeList: [
        {
          label: '自动通过',
          value: 'autoPass'
        },
        {
          label: '通知审核者',
          value: 'messToAssignee'
        },
        {
          label: '通知负责人',
          value: 'messToLeader'
        },
        {
          label: '调用远程接口',
          value: 'remoteUrl'
        }
      ],
      // 跳过策略元数据数组
      skipList: [
        {
          label: '连续相同审核者',
          value: 'same'
        },
        {
          label: '无审核者',
          value: 'nothing'
        },
        {
          label: '已审批过的',
          value: 'passed'
        }
      ],
      // 其他节点属性设置
      otherProperties: {
        // 跳过策略结果
        skip: [],
        overTimeValue: 6,
        remoteUrl: '',
        // 超时策略结果
        overTime: [],
        urlType: '1',
        nodeLevel: 0
      }
    }
  },
  computed: {
    isEvent () {
      if (!this.element) {
        return
      }
      return this.verifyIsEvent(this.element.type)
    },
    isTask () {
      if (!this.element) {
        return
      }
      return this.verifyIsTask(this.element.type)
    },
    isUserTask () {
      if (!this.element) {
        return
      }
      return this.element.type === 'bpmn:UserTask'
    },
    isServiceTask () {
      if (!this.element) {
        return
      }
      return this.element.type === 'bpmn:ServiceTask'
    },
    isStart () {
      if (!this.element) {
        return
      }
      return this.verifyIsStart(this.element.type)
    },
    sequenceFlow () {
      if (!this.element) {
        return
      }
      return this.element.type === 'bpmn:SequenceFlow'
    },
    isExclusiveSequenceFlow () {
      if (!this.element) {
        return
      }
      const { businessObject } = this.element
      if (
        businessObject &&
        businessObject.sourceRef &&
        businessObject.sourceRef.$type === 'bpmn:ExclusiveGateway'
      ) {
        return true
      }
      return false
    },
    isGateway () {
      if (!this.element) {
        return
      }
      return this.verifyIsGateway(this.element.type)
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      const that = this
      // 绑定节点事件
      this.modeler.on('selection.changed', (e) => {
        that.selectedElements = e.newSelection
        that.element = e.newSelection[0]
        that.rootElement = null
        that.setDefaultProperties(that.element)
      })
      this.modeler.on('element.changed', (e) => {
        const { element } = e
        const { element: currentElement } = this
        if (!currentElement) {
          return
        }
        if (element.id === currentElement.id) {
          this.element = element
        }
      })
      this.modeler.on('element.click', (e) => {
        if (!that.element) {
          that.rootElement = e.element
          that.setDefaultProperties(that.rootElement)
        }
      })
      this.modeler.on('root.added', function (e) {
        if (!that.element) {
          that.rootElement = e.element
          that.setDefaultProperties(that.rootElement)
        }
      })
    },
    setDefaultProperties (element) {
      if (element) {
        const { businessObject } = element
        const candidateStarterUsersNames = this.form.candidateStarterUsersNames
        this.form = {
          ...businessObject,
          ...businessObject.$attrs
        }
        this.form.candidateStarterUsersNames = candidateStarterUsersNames
        if (this.isUserTask) {
          this.setUserTaskData(element, businessObject)
        }
        if (this.isServiceTask) {
          const formFields =
            extensionElementsHelper.getExtensionElements(
              businessObject,
              'activiti:CustomForm'
            ) || []
          if (formFields.length === 0) {
            // xml中没有相关表单权限数据
            this.flowService = {
              value: null
            }
          } else {
            this.flowService = {
              value: formFields[0].formValue
            }
          }
        }
        if (this.isTask || this.isStart) {
          this.setCusForms(element, businessObject)
        }
        if (this.isExclusiveSequenceFlow) {
          this.setExclusiveSequenceFlow(element, businessObject)
        }
        this.setListener(element, businessObject)
        this.setListener(element, businessObject)
        this.setTaskListener(element, businessObject)
        if (businessObject.documentation && businessObject.documentation[0]) {
          this.form.description = businessObject.documentation[0].text
        }
      }
    },
    setListener (element, businessObject) {
      // 执行监听器
      this.listenerData = this.listener[element.id] || []
      if (this.listenerData.length === 0) {
        const listeners =
          extensionElementsHelper.getExtensionElements(
            businessObject,
            'activiti:ExecutionListener'
          ) || []
        for (let i = 0; i < listeners.length; i++) {
          const listener = listeners[i]
          const listenerType = ImplementationTypeHelper.getImplementationType(
            listener
          )
          this.listenerData.push({
            id: randomString(16),
            eventType: listener.event || '',
            listenerType: listenerType,
            value: listener[listenerType],
            url: listener[listenerType].slice(
              listener[listenerType].indexOf('http://') + 7,
              listener[listenerType].indexOf('",')
            ),
            executionListener: listener[listenerType].slice(
              listener[listenerType].indexOf('${') + 2,
              listener[listenerType].indexOf('(')
            ),
            isAsyn: listener[listenerType].slice(
              listener[listenerType].lastIndexOf(',') + 1,
              listener[listenerType].indexOf(')}')
            ),
            localFunc: listener[listenerType].slice(
              listener[listenerType].lastIndexOf(',"') + 2,
              listener[listenerType].indexOf('")}')
            )
          })
        }
        this.listener[element.id] = JSON.parse(
          JSON.stringify(this.listenerData)
        )
      }

      if (this.sequenceFlow) {
        this.isShowButton = this.listenerData.length !== 1
      } else {
        this.isShowButton = this.listenerData.length !== 2
      }
    },
    setTaskListener (element, businessObject) {
      // 任务监听器
      this.taskListenerData = this.taskListener[element.id] || []
      if (this.taskListenerData.length === 0) {
        const listeners =
          extensionElementsHelper.getExtensionElements(
            businessObject,
            'activiti:TaskListener'
          ) || []
        for (let i = 0; i < listeners.length; i++) {
          const listener = listeners[i]
          const listenerType = ImplementationTypeHelper.getImplementationType(
            listener
          )
          this.taskListenerData.push({
            id: randomString(16),
            eventType: listener.event || '',
            listenerType: listenerType,
            value: listener[listenerType],
            // 修改时数据回显
            url: listener[listenerType].slice(
              listener[listenerType].indexOf('http://') + 7,
              listener[listenerType].indexOf('",')
            ),
            executionListener: listener[listenerType].slice(
              listener[listenerType].indexOf('${') + 2,
              listener[listenerType].indexOf('(')
            ),
            isAsyn: listener[listenerType].slice(
              listener[listenerType].lastIndexOf(',') + 1,
              listener[listenerType].indexOf(')}')
            ),
            localBean: listener[listenerType].slice(
              listener[listenerType].indexOf(',"') + 2,
              listener[listenerType].indexOf('","')
            ),
            localFunc: listener[listenerType].slice(
              listener[listenerType].lastIndexOf(',"') + 2,
              listener[listenerType].indexOf('")}')
            )
          })
        }
        this.taskListenerData[element.id] = JSON.parse(
          JSON.stringify(this.taskListenerData)
        )
      }
      this.isShowButtonTask = this.taskListenerData.length !== 4
    },
    /**
     * 将xml中互斥网关条件更新到右侧panel中
     */
    setExclusiveSequenceFlow (element, businessObject) {
      if (this.exclusiveSequenceTemp[element.id]) {
        this.exclusiveSequence = this.exclusiveSequenceTemp[element.id]
      } else {
        const tempValue = businessObject.conditionExpression
        console.log('tempValue', tempValue)
        if (tempValue) {
          // const bpmnFactory = this.modeler.get('bpmnFactory')
          // const conditionOrConditionExpression = elementHelper.createElement(
          //   'bpmn:FormalExpression',
          //   {
          //     body: tempValue.body,
          //     resource: tempValue.resource
          //   },
          //   businessObject,
          //   bpmnFactory
          // )
          // const command = cmdHelper.updateBusinessObject(
          //   this.element,
          //   businessObject,
          //   {
          //     conditionExpression: conditionOrConditionExpression
          //   }
          // )
          // this.executeCommand(command)
          const formFields =
            extensionElementsHelper.getExtensionElements(
              businessObject,
              'activiti:CustomForm'
            ) || []
          let flag = ''
          if (formFields.length !== 0) {
            // xml中没有相关表单权限数据
            flag = formFields[0].formValue
          }
          this.exclusiveSequence = {
            conditionExpression: tempValue.body,
            expressionData: decodeURIComponent(tempValue.resource),
            value: flag
          }
          this.exclusiveSequenceTemp[element.id] = JSON.parse(
            JSON.stringify(this.exclusiveSequence)
          )
        } else {
          this.exclusiveSequence = {
            conditionExpression: '',
            expressionData: '',
            value: ''

          }
        }
      }
    },
    /**
     * 将xml中表单权限更新到右侧panel中
     */
    setCusForms (element, businessObject) {
      // 表单字段权限
      if (this.formFieldsTemp[element.id]) {
        this.formFieldsData = this.formFieldsTemp[element.id]
      } else {
        const formFields =
          extensionElementsHelper.getExtensionElements(
            businessObject,
            'activiti:CustomForm'
          ) || []
        if (formFields.length === 0) {
          // xml中没有相关表单权限数据
          this.formFieldsData = {
            formName: '',
            formId: '',
            formKey: '',
            formUrl: '',
            fieldList: []
          }
        } else {
          const tempObj = []
          for (let i = 0; i < formFields.length; i++) {
            const formField = formFields[i]
            tempObj.push({
              formId: formField.formId,
              fieldId: formField.fieldId,
              fieldKey: formField.fieldKey,
              fieldName: formField.fieldName,
              fieldType: formField.fieldType,
              //   visibleFlag: formField.visibleFlag === 'true',
              editFlag:
                formField.editFlag === 'true' || formField.editFlag === true
              //   attachFlag: formField.attachFlag === 'true'
            })
          }
          this.formFieldsData.formName = formFields[0].formName
          this.formFieldsData.formId = formFields[0].formId
          this.formFieldsData.formKey = formFields[0].formKey
          this.formFieldsData.formUrl = formFields[0].formUrl
          this.formFieldsData.fieldList = tempObj
        }
        this.formFieldsTemp[element.id] = JSON.parse(
          JSON.stringify(this.formFieldsData)
        )
      }
      //  this.buildShowFieldStr(this.formFieldsData.fieldList.length > 0 ? this.formFieldsData.fieldList : [])
      this.buildShowFieldStr(this.formFieldsData.fieldList)
    },

    /**
     * 将xml中用户任务的扩展数据（审批对象、按钮权限、会签属性）更新到右侧panel中
     */
    setUserTaskData (element, businessObject) {
      // 按钮
      this.buttonsData = this.buttonsTemp[element.id] || []
      if (this.buttonsData.length === 0) {
        const buttons =
          extensionElementsHelper.getExtensionElements(
            businessObject,
            'activiti:Button'
          ) || []
        for (let i = 0; i < buttons.length; i++) {
          const button = buttons[i]
          this.buttonsData.push({
            name: button.name,
            action: button.code
          })
        }
      }
      this.buttonsTemp[element.id] = JSON.parse(
        JSON.stringify(this.buttonsData)
      )
      // 审批对象
      if (this.approversTemp[element.id]) {
        this.approversData = this.approversTemp[element.id]
      } else {
        const approvers =
          extensionElementsHelper.getExtensionElements(
            businessObject,
            'activiti:Approver'
          ) || []
        if (approvers.length === 0) {
          // xml中没有相关审批对象数据
          this.approversData = {
            choose_user: [],
            choose_post: [],
            process_variables: [],
            creator_self: [],
            submit_self: [],
            creator_superior: [],
            node_superior: [],
            choose_dept: []
          }
        } else {
          const arr1 = approvers.filter(function (element, index, self) {
            return (
              self.findIndex(
                (el) => el.code === element.code && el.type === element.type
              ) === index
            )
          })
          this.approversData = {
            choose_user: [],
            choose_post: [],
            process_variables: [],
            creator_self: [],
            submit_self: [],
            creator_superior: [],
            node_superior: [],
            choose_dept: []
          }
          for (let i = 0; i < arr1.length; i++) {
            const approver = arr1[i]

            if (approver.type === 'choose_user') {
              this.approversData.choose_user.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            } else if (approver.type === 'choose_post') {
              this.approversData.choose_post.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            } else if (approver.type === 'process_variables') {
              this.approversData.process_variables.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            } else if (approver.type === 'creator_self') {
              this.approversData.creator_self.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            } else if (approver.type === 'submit_self') {
              this.approversData.submit_self.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            } else if (approver.type === 'creator_superior') {
              this.approversData.creator_superior.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            } else if (approver.type === 'node_superior') {
              this.approversData.node_superior.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            } else if (approver.type === 'choose_dept') {
              this.approversData.choose_dept.push({
                name: approver.name,
                code: approver.code,
                type: approver.type
              })
            }
          }
        }
        this.approversTemp[element.id] = JSON.parse(
          JSON.stringify(this.approversData)
        )
      }
      // 会签属性
      if (this.multiPropTemp[element.id]) {
        this.multiProp = this.multiPropTemp[element.id]
      } else {
        const loopCharacteristics = businessObject.loopCharacteristics
        if (loopCharacteristics) {
          const multiUserTaskPercentValueValue = this.getMultiUserTaskPercentValueValue(
            loopCharacteristics
          )
          this.multiProp = {
            multiUserTaskFlag: loopCharacteristics.isSequential ? 2 : 1,
            multiUserTaskWay:
              loopCharacteristics.completionCondition.body ===
              '${agreeVotesCounts == multiUserTaskUsersSize ' + '}'
                ? 'all'
                : 'percent',
            multiUserTaskPercentValue: multiUserTaskPercentValueValue
          }
          this.multiPropTemp[element.id] = JSON.parse(
            JSON.stringify(this.multiProp)
          )
        } else {
          this.multiProp = {
            multiUserTaskFlag: 8,
            multiUserTaskWay: 'all',
            multiUserTaskPercentValue: 100
          }
        }
      }
      // 当前节点是会签的话,去掉跳过的配置;不是会签则将跳过配置展示出来
      if (this.multiProp.multiUserTaskFlag === 8) {
        this.skipShowFlag = true
      } else {
        this.skipShowFlag = false
      }
      // 跳过策略
      this.otherProperties.skip = this.skipValuesTemp[element.id] || []
      if (this.otherProperties.skip.length === 0) {
        const skips =
          extensionElementsHelper.getExtensionElements(
            businessObject,
            'activiti:Skip'
          ) || []
        for (let i = 0; i < skips.length; i++) {
          const skip = skips[i]
          this.otherProperties.skip.push(skip.code)
        }
        this.skipValuesTemp[element.id] = JSON.parse(
          JSON.stringify(this.otherProperties.skip)
        )
      }

      // 节点级别
      this.otherProperties.nodeLevel = this.nodeLevelValuesTemp[element.id] || 0
      const nodeLevel = extensionElementsHelper.getExtensionElements(businessObject, 'activiti:NodeLevel') || []
      if (nodeLevel.length !== 0) {
        this.otherProperties.nodeLevel = nodeLevel[0].code
        this.nodeLevelValuesTemp[element.id] = JSON.parse(JSON.stringify(this.otherProperties.nodeLevel))
      }

      // 超时策略
      this.otherProperties.overTimeValue = this.overTimeValuesTemp[element.id] || 6
      this.otherProperties.remoteUrl = this.overTimeUrlTemp[element.id] || ''
      this.otherProperties.urlType = this.overTimeUrlTypeTemp[element.id] || '1'
      this.otherProperties.overTime = this.overTimeTemp[element.id] || []
      if (this.otherProperties.overTime.length === 0) {
        this.isOverTime = false
        const overTimes =
          extensionElementsHelper.getExtensionElements(
            businessObject,
            'activiti:OverTime'
          ) || []
        let hasRemoteUrl = false
        for (let i = 0; i < overTimes.length; i++) {
          const overTime = overTimes[i]
          this.otherProperties.overTime.push(overTime.code)
          this.otherProperties.overTimeValue = overTime.overTimeValue
          this.otherProperties.nodeLevel = overTime.nodeLevel
          if (overTime.code === 'remoteUrl') {
            hasRemoteUrl = true
            this.otherProperties.remoteUrl = overTime.remoteUrl
            this.otherProperties.urlType = overTime.urlType
          }
        }
        if (hasRemoteUrl) {
          this.isShowRemoteUrl = true
          this.isShowUrlType = true
        } else {
          this.isShowRemoteUrl = false
          this.isShowUrlType = false
        }
        this.overTimeValuesTemp[element.id] = JSON.parse(
          JSON.stringify(this.otherProperties.overTimeValue)
        )
        this.overTimeUrlTemp[element.id] = this.otherProperties.remoteUrl
        this.overTimeUrlTypeTemp[element.id] = this.otherProperties.urlType
        this.overTimeTemp[element.id] = JSON.parse(
          JSON.stringify(this.otherProperties.overTime)
        )
      }
      if (this.otherProperties.overTime.length !== 0) {
        this.isOverTime = true
      } else {
        this.isShowRemoteUrl = false
        this.isShowUrlType = false
      }
      // 是否显示
      for (let i = 0; i < this.otherProperties.overTime.length; i++) {
        const overTimeItem = this.otherProperties.overTime[i]
        if (overTimeItem === 'remoteUrl') {
          this.isShowRemoteUrl = true
          this.isShowUrlType = true
        } else {
          this.isShowRemoteUrl = false
          this.isShowUrlType = false
        }
      }
      // 节点级别
      if (this.otherProperties.nodeLevel !== null && this.otherProperties.nodeLevel === '') {
        this.nodeLevelValuesTemp[element.id] = JSON.parse(JSON.stringify(this.otherProperties.nodeLevel))
      }
    },
    /**
     * 解析表达式，获得设置的会签百分比信息
     */
    getMultiUserTaskPercentValueValue (loopCharacteristics) {
      let res = 100
      //  ${agreeVotesCounts/multiUserTaskUsersSize >= 1}
      if (
        !(
          loopCharacteristics.completionCondition.body ===
          '${agreeVotesCounts == multiUserTaskUsersSize ' + '}'
        )
      ) {
        let valueStr = loopCharacteristics.completionCondition.body.substring(
          45
        )
        valueStr = valueStr.substring(0, valueStr.length - 1)
        res = valueStr * 100
      }
      return res
    },
    verifyIsEvent (type) {
      if (!type) return
      return type.includes('Event')
    },
    verifyIsTask (type) {
      if (!type) return
      return type.includes('Task')
    },
    verifyIsStart (type) {
      return type === 'bpmn:StartEvent'
    },
    verifyIsGateway (type) {
      if (!type) return
      return type.includes('Gateway')
    },
    /**
     * 改变控件触发的事件
     * @param { Object } input的Event
     * @param { String } 要修改的属性的名称
     */
    changeField (event, type) {
      const value = event.target.value
      this.element[type] = value
      const properties = {}
      properties[type] = value
      if (type === 'color') {
        this.onChangeColor(value)
      }
      this.updateProperties(properties)
    },
    updateName (name) {
      this.updateProperties({ name: name })
    },
    /**
     * 更新流程key
     */
    updateKey (key) {
      this.updateProperties({ id: key })
    },
    changeEventType (event) {
      const { modeler, element } = this
      const value = event.target.value
      const bpmnReplace = modeler.get('bpmnReplace')
      this.eventType = value
      bpmnReplace.replaceElement(element, {
        type: element.businessObject.$type,
        eventDefinitionType: value
      })
    },
    changeTaskType (event) {
      const { modeler, element } = this
      const value = event.target.value
      const bpmnReplace = modeler.get('bpmnReplace')
      bpmnReplace.replaceElement(element, {
        type: value
      })
    },
    /**
     * 更新元素属性
     * @param { Object } 要更新的属性, 例如 { name: '' }
     */
    updateProperties (properties) {
      const modeling = this.modeler.get('modeling')
      modeling.updateProperties(
        this.element ? this.element : this.rootElement,
        properties
      )
    },
    // 文档描述设置
    updateDocumentation (value) {
      const bpmnFactory = this.modeler.get('bpmnFactory')
      if (value) {
        const newObjectList = []
        newObjectList.push(
          bpmnFactory.create('bpmn:Documentation', {
            text: value
          })
        )
        const element = this.rootElement
        const command = cmdHelper.setList(
          element,
          element.businessObject,
          'documentation',
          newObjectList
        )
        this.executeCommand(command)
      }
    },
    // 条件分支设置
    updateConditionExpression () {
      if (!this.element) {
        return
      }
      console.log('this.exclusiveSequence.conditionExpression', this.exclusiveSequence.conditionExpression)
      const { businessObject } = this.element
      const bpmnFactory = this.modeler.get('bpmnFactory')

      const conditionOrConditionExpression = elementHelper.createElement(
        'bpmn:FormalExpression',
        {
          body: this.exclusiveSequence.conditionExpression,
          resource: encodeURIComponent(this.exclusiveSequence.expressionData)
        },
        businessObject,
        bpmnFactory
      )
      const command = cmdHelper.updateBusinessObject(
        this.element,
        businessObject,
        {
          conditionExpression: conditionOrConditionExpression
        }
      )
      this.executeCommand(command)
      this.exclusiveSequenceTemp[this.element.id] = this.exclusiveSequence
    },
    // 条件分支设置
    handleRuleConfigCallback (expression) {
      if (!this.element) {
        return
      }

      const { businessObject } = this.element
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const modeling = this.modeler.get('modeling')
      if (businessObject.conditionExpression) {
        modeling.updateProperties(this.element, {
          conditionExpression: null
        })
      }
      let extensionElements = businessObject.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          businessObject,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(this.element, businessObject, {
            extensionElements: extensionElements
          })
        )
      }
      const newElems = []
      const props = {
        formValue: expression
      }
      const newElem = elementHelper.createElement(
        'activiti:CustomForm',
        props,
        extensionElements,
        bpmnFactory
      )
      newElems.push(newElem)
      // 先将所有已设置的数据删除掉，再重新添加
      const CustomFormOlds =
        extensionElementsHelper.getExtensionElements(
          this.element.businessObject,
          'activiti:CustomForm'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          this.element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          CustomFormOlds
        )
      )
      const nodeExpression = '_' + `${this.element.id}_conditionExpression` + '_'
      const conditionExpression = bpmnFactory.create('bpmn:FormalExpression', {
        body: '${' + nodeExpression + '}'
      })
      modeling.updateProperties(this.element, {
        conditionExpression: conditionExpression
      })
      this.exclusiveSequence.value = expression
      this.exclusiveSequence.conditionExpression = '${' + nodeExpression + '}'
      this.exclusiveSequenceTemp[this.element.id] = this.exclusiveSequence
    },
    executeCommand (command) {
      const commandStack = this.modeler.get('commandStack')
      commandStack.execute(command.cmd, command.context)
    },
    addListeners: function (isTaskListener) {
      this.isTaskListener = isTaskListener
      let _this = this.$refs.FlowListener
      if (isTaskListener) {
        // 任务监听器
        _this = this.$refs.FlowTaskListener
      }
      _this.isDisabled = false
      _this.showCheck()
      _this.dialog = true
    },
    addServiceTask: function () {
      this.$refs.FlowService.dialog = true
      // _this.showCheck()
    },
    handleServiceCallback (val) {
      const element = this.rootElement ? this.rootElement : this.element
      const listenerType = val.listenerType
      const value = val.value
      this.flowService = val
      this.executeCommand(
        cmdHelper.updateProperties(element, {
          [listenerType]: value
        })
      )
    },
    handleBusinessRuleConfigCallback (val) {
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let extensionElements = bo.extensionElements
      const execution = 'myJavaServiceTask.execute(execution)'
      const executeExecution = '${' + execution + '}'
      this.executeCommand(
        cmdHelper.updateProperties(element, {
          expression: executeExecution
        }))

      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      const newElems = []
      const props = {
        formValue: val
      }
      const newElem = elementHelper.createElement(
        'activiti:CustomForm',
        props,
        extensionElements,
        bpmnFactory
      )
      newElems.push(newElem)
      // 先将所有已设置的数据删除掉，再重新添加
      const CustomFormOlds =
        extensionElementsHelper.getExtensionElements(
          element.businessObject,
          'activiti:CustomForm'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          CustomFormOlds
        )
      )
      this.flowService.value = val
    },
    handleListenerCallback (val) {
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let type = ''
      let i = 0
      // 执行监听器
      if (!this.isTaskListener) {
        type = 'activiti:ExecutionListener'
        for (i = 0; i < this.listenerData.length; i++) {
          const data = this.listenerData[i]
          if (data.id === val.id) {
            // this.listenerData.splice(i)
            this.listenerData.splice(i, 1)
            this.executeCommand(
              this.removeExtensionElement(element, bo, type, i)
            )
            break
          }
        }
        // this.listenerData.push(val)
        this.listenerData.splice(i, 0, val)
        this.listener[element.id] = JSON.parse(
          JSON.stringify(this.listenerData)
        )
        if (this.sequenceFlow) {
          this.isShowButton = this.listenerData.length !== 1
        } else {
          this.isShowButton = this.listenerData.length !== 2
        }
      } else {
        type = 'activiti:TaskListener'
        for (i = 0; i < this.taskListenerData.length; i++) {
          const data = this.taskListenerData[i]
          if (data.id === val.id) {
            // this.taskListenerData.splice(i)
            this.taskListenerData.splice(i, 1)
            this.executeCommand(
              this.removeExtensionElement(element, bo, type, i)
            )
            break
          }
        }
        // this.taskListenerData.push(val)
        this.taskListenerData.splice(i, 0, val)
        this.taskListener[element.id] = JSON.parse(
          JSON.stringify(this.taskListenerData)
        )
        this.isShowButtonTask = this.taskListenerData.length !== 4
      }
      let extensionElements = bo.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      this.executeCommand(
        this.createExtensionElement(
          element,
          type,
          extensionElements,
          val,
          bpmnFactory
        )
      )
    },
    // 创建扩展元素
    createExtensionElement (
      element,
      type,
      extensionElements,
      value,
      bpmnFactory
    ) {
      const props = {
        event: value.eventType
      }
      props[value.listenerType] = value.value
      const newElem = elementHelper.createElement(
        type,
        props,
        extensionElements,
        bpmnFactory
      )
      return cmdHelper.addElementsTolist(element, extensionElements, 'values', [
        newElem
      ])
    },
    // 删除扩展元素
    removeExtensionElement (element, bo, type, idx) {
      const listeners =
        extensionElementsHelper.getExtensionElements(bo, type) || []
      const listener = listeners[idx]
      if (listener) {
        return extensionElementsHelper.removeEntry(bo, element, listener)
      }
    },
    updateListener (data, isTaskListener) {
      // 禁用事件类型
      this.isTaskListener = isTaskListener
      let _this = this.$refs.FlowListener
      if (isTaskListener) {
        _this = this.$refs.FlowTaskListener
      }
      _this.form = {
        ...data
      }
      _this.dialog = true
      _this.isDisabled = true
    },
    ruleConfig () {
      console.log('初始化方法：this.exclusiveSequence.value',this.exclusiveSequence.value)
      this.$refs.RuleConfig.dialog = true
      this.$nextTick(() => {
        this.$refs.RuleConfig.parseExpression(this.exclusiveSequence.value)
      })
    },
    businessRuleConfig () {
      this.$refs.BusinessRuleConfig.dialog = true
      this.$nextTick(() => {
        this.$refs.BusinessRuleConfig.parseExpression(this.flowService.value)
      })
    },
    listenerDel (val, isTaskListener) {
      this.isTaskListener = isTaskListener
      let idx
      let type
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      // 执行监听器
      if (!isTaskListener) {
        idx = this.listenerData.indexOf(val)
        this.listenerData.splice(idx, 1)
        type = 'activiti:ExecutionListener'
        this.isShowButton = true
      } else {
        idx = this.taskListenerData.indexOf(val)
        this.taskListenerData.splice(idx, 1)
        type = 'activiti:TaskListener'
        this.isShowButtonTask = true
      }
      this.executeCommand(this.removeExtensionElement(element, bo, type, idx))
    },
    serviceTaskDel () {
      // 执行监听器
      this.flowService = {}
      const properties = { 'activiti:class': null }
      const modeling = this.modeler.get('modeling')
      modeling.updateProperties(
        this.element ? this.element : this.rootElement,
        properties
      )
    },
    // 选择按钮权限
    addButton () {
      const _this = this.$refs.FlowButton
      _this.dialog = true
      _this.multipleSelection = this.buttonsData
      _this.$nextTick(() => {
        this.buttonsData.forEach((row) => {
          _this.$refs.multipleTable.toggleRowSelection(row)
        })
      })
    },
    // 选择表单及可编辑的字段
    addCustomForms () {
      const _this = this.$refs.CustomFormSelect
      _this.bussinessId = this.bussinessId
      _this.$nextTick(() => {
        _this.init(this.formFieldsData)
      })
      _this.dialog = true
    },
    // 选择审核者
    addApprovers () {
      const arr = []
      this.modeler.saveXML({ format: true }, function (err, xml) {
        xml = err ? null : xml
        // 创建文档对象
        const parser = new DOMParser()
        const xmlDoc = parser.parseFromString(xml, 'text/xml')

        // 提取数据
        const countrys = xmlDoc.getElementsByTagName('userTask')
        // 将已经选择的用户传递给前端
        for (let i = 0; i < countrys.length; i++) {
          const objectxml = countrys[i].children
          // 判断是否存在会签节点,如果是会签节点则剔除
          let isMultiInstance = false
          for (let i = 0; i < objectxml.length; i++) {
            const xmlString = new XMLSerializer().serializeToString(
              objectxml[i]
            )
            const isHas = xmlString.indexOf('multiInstanceLoopCharacteristics')
            if (isHas !== -1) {
              isMultiInstance = true
            }
          }
          if (!isMultiInstance) {
            const chackNode = {
              code: countrys[i].attributes.id.value,
              name:
                countrys[i].attributes.name === undefined ? ''
                  : countrys[i].attributes.name.value
            }
            arr.push(chackNode)
          }
        }
      })

      const _this = this.$refs.ApproveSelect
      // 将数据回显到选择器上
      _this.approved = this.changeToApproved()
      _this.choicekNode = arr
      if (_this.approved.length === 0) {
        _this.approved = [
          {
            uuid: util.getUUID(),
            approveType: 'choose_user',
            approvers: '',
            approversObj: [],
            chooseBtnFlag: true
          }
        ]
      }
      _this.dialog = true
    },
    // 将此组件上的审批对象数据，转换为选择审批对象组件中的数据格式
    changeToApproved () {
      // 此时最终应该是【发起人】【当前登录用户】【指定用户】【岗位】【部门】五条数据
      const approved = []
      let userApproversStr = ''
      const userApproversObj = []
      for (let i = 0; i < this.approversData.choose_user.length; i++) {
        userApproversStr += this.approversData.choose_user[i].name + ','
        userApproversObj.push({
          code: this.approversData.choose_user[i].code,
          name: this.approversData.choose_user[i].name
        })
      }
      if (userApproversObj.length > 0) {
        approved.push({
          uuid: util.getUUID(),
          approveType: 'choose_user',
          approvers: userApproversStr.substring(0, userApproversStr.length - 1),
          approversObj: userApproversObj,
          chooseBtnFlag: true
        })
      }
      // 审批角色(由于角色是不能合并的，这个需要特殊处理)
      for (let k = 0; k < this.approversData.choose_post.length; k++) {
        let postApproversStr = ''
        const postApproversObj = []
        postApproversStr = '【' + this.approversData.choose_post[k].name + '】'
        postApproversObj.push({
          code: this.approversData.choose_post[k].code,
          name: this.approversData.choose_post[k].name
        })
        approved.push({
          uuid: util.getUUID(),
          approveType: 'choose_post',
          approvers: postApproversStr,
          approversObj: postApproversObj,
          chooseBtnFlag: true
        })
      }
      // 流程变量(由于流程变量是不能合并的，这个需要特殊处理)
      for (let k = 0; k < this.approversData.process_variables.length; k++) {
        let variableApproversStr = ''
        const variableApproversObj = []
        variableApproversStr = '【' + this.approversData.process_variables[k].name + '】'
        variableApproversObj.push({
          code: this.approversData.process_variables[k].code,
          name: this.approversData.process_variables[k].name
        })
        approved.push({
          uuid: util.getUUID(),
          approveType: 'process_variables',
          approvers: variableApproversStr,
          approversObj: variableApproversObj,
          chooseBtnFlag: true
        })
      }
      // 指定节点的上级部门(不能合并)
      for (let k = 0; k < this.approversData.node_superior.length; k++) {
        let postApproversStr = ''
        const postApproversObj = []
        postApproversStr =
          '【' + this.approversData.node_superior[k].name + '】'
        postApproversObj.push({
          code: this.approversData.node_superior[k].code,
          name: this.approversData.node_superior[k].name
        })
        approved.push({
          uuid: util.getUUID(),
          approveType: 'node_superior',
          approvers: postApproversStr,
          approversObj: postApproversObj,
          chooseBtnFlag: true
        })
      }
      // 流程发起人
      let selfApproversStr = ''
      const selfApproversObj = []
      for (let k = 0; k < this.approversData.creator_self.length; k++) {
        selfApproversStr +=
          '【' + this.approversData.creator_self[k].name + '】'
        selfApproversObj.push({
          code: this.approversData.creator_self[k].code,
          name: this.approversData.creator_self[k].name
        })
      }
      if (selfApproversObj.length > 0) {
        approved.push({
          uuid: util.getUUID(),
          approveType: 'creator_self',
          approvers: selfApproversStr,
          approversObj: selfApproversObj,
          chooseBtnFlag: false
        })
      }

      // 流程发起人(审批)
      let submitApproversStr = ''
      const submitApproversObj = []
      for (let k = 0; k < this.approversData.submit_self.length; k++) {
        submitApproversStr +=
          '【' + this.approversData.submit_self[k].name + '】'
        submitApproversObj.push({
          code: this.approversData.submit_self[k].code,
          name: this.approversData.submit_self[k].name
        })
      }
      if (submitApproversObj.length > 0) {
        approved.push({
          uuid: util.getUUID(),
          approveType: 'submit_self',
          approvers: submitApproversStr,
          approversObj: submitApproversObj,
          chooseBtnFlag: false
        })
      }
      // 发起人直接上级
      let superiorApproversStr = ''
      const superiorApproversObj = []
      for (let k = 0; k < this.approversData.creator_superior.length; k++) {
        superiorApproversStr +=
          '【' + this.approversData.creator_superior[k].name + '】'
        superiorApproversObj.push({
          code: this.approversData.creator_superior[k].code,
          name: this.approversData.creator_superior[k].name
        })
      }

      if (superiorApproversObj.length > 0) {
        approved.push({
          uuid: util.getUUID(),
          approveType: 'creator_superior',
          approvers: superiorApproversStr,
          approversObj: superiorApproversObj,
          chooseBtnFlag: false
        })
      }
      // 部门
      let deptApproversStr = ''
      const deptApproversObj = []
      for (let l = 0; l < this.approversData.choose_dept.length; l++) {
        deptApproversStr +=
          '【' + this.approversData.choose_dept[l].name + '】'
        deptApproversObj.push({
          code: this.approversData.choose_dept[l].code,
          name: this.approversData.choose_dept[l].name
        })
      }
      if (deptApproversObj.length > 0) {
        approved.push({
          uuid: util.getUUID(),
          approveType: 'choose_dept',
          approvers: deptApproversStr,
          approversObj: deptApproversObj,
          chooseBtnFlag: false
        })
      }
      return approved
    },
    // 选择审批对象后的回调函数
    handleCallback (datas) {
      /**
       * 1、对数据进行去重、分组处理
       */
      this.approversData = this.removeDuplicates(datas)
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let extensionElements = bo.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      const newElems = []
      // 用户
      for (let i = 0; i < this.approversData.choose_user.length; i++) {
        const approver = this.approversData.choose_user[i]
        const props = {
          name: approver.name,
          code: approver.code,
          type: approver.type
        }
        const newElem = elementHelper.createElement(
          'activiti:Approver',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      for (let i = 0; i < this.approversData.choose_post.length; i++) {
        const approver = this.approversData.choose_post[i]
        const props = {
          name: approver.name,
          code: approver.code,
          type: approver.type
        }
        const newElem = elementHelper.createElement(
          'activiti:Approver',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      for (let i = 0; i < this.approversData.process_variables.length; i++) {
        const approver = this.approversData.process_variables[i]
        const props = {
          name: approver.name,
          code: approver.code,
          type: approver.type
        }
        const newElem = elementHelper.createElement(
          'activiti:Approver',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      for (let i = 0; i < this.approversData.creator_self.length; i++) {
        const approver = this.approversData.creator_self[i]
        const props = {
          name: approver.name,
          code: approver.code,
          type: approver.type
        }
        const newElem = elementHelper.createElement(
          'activiti:Approver',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      // 流程发起人审批
      for (let i = 0; i < this.approversData.submit_self.length; i++) {
        const approver = this.approversData.submit_self[i]
        const props = {
          name: approver.name,
          code: approver.code,
          type: approver.type
        }
        const newElem = elementHelper.createElement(
          'activiti:Approver',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      // 指定节点审批人的上级节点
      for (let i = 0; i < this.approversData.node_superior.length; i++) {
        const approver = this.approversData.node_superior[i]
        const props = {
          name: approver.name,
          code: approver.code,
          type: approver.type
        }
        const newElem = elementHelper.createElement(
          'activiti:Approver',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      for (let i = 0; i < this.approversData.creator_superior.length; i++) {
        const approver = this.approversData.creator_superior[i]
        const props = {
          name: approver.name,
          code: approver.code,
          type: approver.type
        }
        const newElem = elementHelper.createElement(
          'activiti:Approver',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      // 先将所有已设置的数据删除掉，再重新添加
      const approverOlds =
        extensionElementsHelper.getExtensionElements(
          element.businessObject,
          'activiti:Approver'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          approverOlds
        )
      )
      this.approversTemp[element.id] = JSON.parse(
        JSON.stringify(this.approversData)
      )
    },
    /**
     * 去除已选审批对象的重复项，并分个组
     */
    removeDuplicates (oldData) {
      // 1、先将所有元数据放到一起
      const newData = []
      for (let i = 0; i < oldData.length; i++) {
        const approversObjTemp = oldData[i].approversObj
        const typeStr = oldData[i].approveType
        for (let j = 0; j < approversObjTemp.length; j++) {
          approversObjTemp[j].type = typeStr
          newData.push(approversObjTemp[j])
        }
      }
      // 对现有元素进行去重
      const arr1 = newData.filter(function (element, index, self) {
        return (
          self.findIndex(
            (el) => el.code === element.code && el.type === element.type
          ) === index
        )
      })
      // 分组
      const userArr = []
      const postArr = []
      const variableArr = []
      const deptArr = []
      const selfArr = []
      const submitArr = []
      const nodeArr = []
      const superiortArr = []
      for (let k = 0; k < arr1.length; k++) {
        if (arr1[k].type === 'choose_user') {
          userArr.push(arr1[k])
        } else if (arr1[k].type === 'choose_post') {
          postArr.push(arr1[k])
        } else if (arr1[k].type === 'process_variables') {
          variableArr.push(arr1[k])
        } else if (arr1[k].type === 'choose_dept') {
          deptArr.push(arr1[k])
        } else if (arr1[k].type === 'creator_self') {
          selfArr.push(arr1[k])
        } else if (arr1[k].type === 'submit_self') {
          submitArr.push(arr1[k])
        } else if (arr1[k].type === 'creator_superior') {
          superiortArr.push(arr1[k])
        } else if (arr1[k].type === 'node_superior') {
          nodeArr.push(arr1[k])
        }
      }

      const finalResult = {
        choose_user: userArr,
        choose_post: postArr,
        process_variables: variableArr,
        choose_dept: deptArr,
        creator_self: selfArr,
        submit_self: submitArr,
        creator_superior: superiortArr,
        node_superior: nodeArr
      }
      return finalResult
    },
    // 选择按钮权限后的回调函数
    handleButtonCallback (datas) {
      this.buttonsData = datas
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let extensionElements = bo.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      const newElems = []
      for (let i = 0; i < this.buttonsData.length; i++) {
        const button = this.buttonsData[i]
        const props = {
          name: button.name,
          code: button.action
        }
        const newElem = elementHelper.createElement(
          'activiti:Button',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      // 先将所有已设置的数据删除掉，再重新添加
      const buttonOlds =
        extensionElementsHelper.getExtensionElements(
          element.businessObject,
          'activiti:Button'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          buttonOlds
        )
      )
      this.buttonsTemp[element.id] = JSON.parse(
        JSON.stringify(this.buttonsData)
      )
    },
    removeButton (data) {
      const idx = this.buttonsData.indexOf(data)
      const type = 'activiti:Button'
      this.buttonsData.splice(idx, 1)
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      this.executeCommand(this.removeExtensionElement(element, bo, type, idx))
      this.buttonsTemp[element.id] = JSON.parse(
        JSON.stringify(this.buttonsData)
      )
    },
    // 多实例的生成
    multiInstance () {
      let isSequentialVal = false
      if (this.multiProp.multiUserTaskFlag === 8) {
        this.modeler.get('modeling').updateProperties(this.element, {
          loopCharacteristics: undefined
        })
        this.multiProp.multiUserTaskFlag = 8
        this.multiProp.multiUserTaskPercentValue = 100
        this.skipShowFlag = true
        return
      } else {
        // 去掉跳过的配置
        this.otherProperties.skip = []
        this.otherProperties.nodeLevel = 0
        this.skipChange()
        this.skipShowFlag = false
      }
      if (this.multiProp.multiUserTaskFlag === 2) {
        isSequentialVal = true
      }
      const modeling = this.modeler.get('moddle')
      // 后台需要的变量
      var bianliang = '${'
      if (this.multiProp.multiUserTaskWay === 'all') {
        bianliang += 'agreeVotesCounts == multiUserTaskUsersSize }'
      } else {
        if (this.multiProp.multiUserTaskPercentValue) {
          bianliang +=
            'agreeVotesCounts/multiUserTaskUsersSize >= ' +
            this.multiProp.multiUserTaskPercentValue / 100 +
            '}'
        } else {
          bianliang += 'agreeVotesCounts/multiUserTaskUsersSize >= 1' + '}'
        }
      }
      // 添加变量
      var experession = modeling.create('bpmn:Expression', { body: bianliang })
      const loopCharacteristics = modeling.create(
        'bpmn:MultiInstanceLoopCharacteristics',
        {
          isSequential: isSequentialVal,
          collection: '${' + 'multiUserTaskUsers}',
          //  collection: 'multiUserTaskUsers',
          completionCondition: experession
        }
      )
      // 添加多实例的属性
      this.modeler.get('modeling').updateProperties(this.element, {
        loopCharacteristics: loopCharacteristics
      })
      this.multiPropTemp[this.element.id] = this.multiProp
    },

    // 节点级别
    nodeLevelChange () {
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let extensionElements = bo.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      const newElems = []
      const props = {
        code: this.otherProperties.nodeLevel
      }
      const newElem = elementHelper.createElement(
        'activiti:NodeLevel',
        props,
        extensionElements,
        bpmnFactory
      )
      newElems.push(newElem)
      // 先将所有已设置的数据删除掉，再重新添加
      const SkipOlds =
        extensionElementsHelper.getExtensionElements(
          element.businessObject,
          'activiti:NodeLevel'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          SkipOlds
        )
      )
      this.skipValuesTemp[element.id] = JSON.parse(
        JSON.stringify(this.otherProperties.nodeLevel)
      )
    },

    // 跳过策略的生成
    skipChange () {
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let extensionElements = bo.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      const newElems = []
      for (let i = 0; i < this.otherProperties.skip.length; i++) {
        const skipItem = this.otherProperties.skip[i]
        const props = {
          code: skipItem
        }
        const newElem = elementHelper.createElement(
          'activiti:Skip',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      // 先将所有已设置的数据删除掉，再重新添加
      const SkipOlds =
        extensionElementsHelper.getExtensionElements(
          element.businessObject,
          'activiti:Skip'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          SkipOlds
        )
      )
      this.skipValuesTemp[element.id] = JSON.parse(
        JSON.stringify(this.otherProperties.skip)
      )
    },
    // 超时策略的生成
    overTimeChange () {
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let extensionElements = bo.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      if (this.otherProperties.overTime.length !== 0) {
        this.isOverTime = true
        for (let i = 0; i < this.otherProperties.overTime.length; i++) {
          const value = this.otherProperties.overTime[i]
          if (value === 'remoteUrl') {
            this.isShowRemoteUrl = true
            this.isShowUrlType = true
            break
          } else {
            this.isShowRemoteUrl = false
            this.isShowUrlType = false
          }
        }
      } else {
        this.isOverTime = false
        this.isShowRemoteUrl = false
        this.isShowUrlType = false
      }
      const newElems = []
      for (let i = 0; i < this.otherProperties.overTime.length; i++) {
        const overTimeItem = this.otherProperties.overTime[i]
        const props = {
          code: overTimeItem,
          overTimeValue: this.otherProperties.overTimeValue,
          remoteUrl: this.otherProperties.remoteUrl,
          urlType: this.otherProperties.urlType
        }
        const newElem = elementHelper.createElement(
          'activiti:OverTime',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      // 先将所有已设置的数据删除掉，再重新添加
      const OverTimeOlds =
        extensionElementsHelper.getExtensionElements(
          element.businessObject,
          'activiti:OverTime'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          OverTimeOlds
        )
      )

      this.overTimeValuesTemp[element.id] = JSON.parse(
        JSON.stringify(this.otherProperties.overTimeValue)
      )
      this.overTimeUrlTemp[element.id] = this.otherProperties.remoteUrl
      this.overTimeUrlTypeTemp[element.id] = this.otherProperties.urlType
      this.nodeLevelValuesTemp[element.id] = JSON.parse(
        JSON.stringify(this.otherProperties.nodeLevel)
      )
      this.overTimeTemp[element.id] = JSON.parse(
        JSON.stringify(this.otherProperties.overTime)
      )
    },

    // 选择表单字段权限之后的回调函数
    handleCustomFormCallback (formAndFieldModel) {
      const bpmnFactory = this.modeler.get('bpmnFactory')
      const element = this.rootElement ? this.rootElement : this.element
      const bo = element.businessObject
      let extensionElements = bo.extensionElements
      if (!extensionElements) {
        extensionElements = elementHelper.createElement(
          'bpmn:ExtensionElements',
          { values: [] },
          bo,
          bpmnFactory
        )
        this.executeCommand(
          cmdHelper.updateBusinessObject(element, bo, {
            extensionElements: extensionElements
          })
        )
      }
      const newElems = []
      for (let i = 0; i < formAndFieldModel.fieldList.length; i++) {
        const formField = formAndFieldModel.fieldList[i]
        const props = {
          formId: formAndFieldModel.formId,
          formName: formAndFieldModel.formName,
          formKey: formAndFieldModel.formKey,
          formUrl: formAndFieldModel.formUrl,
          fieldId: formField.fieldId,
          fieldKey: formField.fieldKey,
          fieldName: formField.fieldName,
          fieldType: formField.fieldType,
          // visibleFlag: formField.visibleFlag,
          editFlag: formField.editFlag
          // attachFlag: formField.attachFlag
        }
        const newElem = elementHelper.createElement(
          'activiti:CustomForm',
          props,
          extensionElements,
          bpmnFactory
        )
        newElems.push(newElem)
      }
      // 先将所有已设置的数据删除掉，再重新添加
      const CustomFormOlds =
        extensionElementsHelper.getExtensionElements(
          element.businessObject,
          'activiti:CustomForm'
        ) || []
      this.executeCommand(
        cmdHelper.addAndRemoveElementsFromList(
          element,
          extensionElements,
          'values',
          'extensionElements',
          newElems,
          CustomFormOlds
        )
      )
      this.formFieldsTemp[element.id] = JSON.parse(
        JSON.stringify(formAndFieldModel)
      )
      this.formFieldsData = formAndFieldModel
      // 改变展示的字符串
      this.buildShowFieldStr(formAndFieldModel.fieldList)
    },
    // 拼装str
    buildShowFieldStr (arr) {
      let cfieldStr = ''
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].editFlag === true || arr[i].editFlag === 'true') {
          cfieldStr += '【' + arr[i].fieldName + '】'
        }
      }
      this.fieldStr = cfieldStr
    }
  }
}
</script>

<style lang="scss">
.property-panel {
  width: 398px;
  height: 500px;
}

.el_title {
  padding-left: 20px;
}

.el_input {
  width: 280px;
}

.el-card__header {
  padding: 5px 10px;
  border-bottom: 1px solid #ebeef5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-weight: bold;
  background-color: #e5f2ff;
}
</style>
