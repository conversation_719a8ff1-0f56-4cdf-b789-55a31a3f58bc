<template>
  <div class="drugDiameter" :style="{ height: heightY - 100 + 'px' }">
    <div class="drugDiameter-left">
      <div class="filter-left-top-box">
        <div class="filter-title">
          <span>科室</span>
        </div>
        <el-input placeholder="输入关键字过滤科室" v-model="filterTextDept" size="small" clearable>
        </el-input>
        <el-tree class="filter-tree-dept" :data="dataDept" :props="defaultProps" default-expand-all
                 style="height: calc(100% - 70px);margin-top: 5px;" :filter-node-method="filterNodeDept"
                 ref="treeDept" :expand-on-click-node="false" :highlight-current="true" @node-click="chooseDept">
        </el-tree>
      </div>
      <div class="filter-left-bottom-box">
        <div class="filter-title">
          <span>药径类别</span>
        </div>
        <div>
          <el-input placeholder="输入关键字过滤药径类别" v-model="filterTextType" size="small" clearable
                    style="width: calc(100% - 60px);">
          </el-input>
          <el-button type="primary" icon="el-icon-plus" size="small" style="margin-left: 8px;"
                     @click="addTypeTop()"></el-button>
        </div>
        <el-tree class="filter-tree-dept" :data="dataType" :props="defaultPropsType" default-expand-all
                 style="height: calc(100% - 65px);margin-top: 5px;" :filter-node-method="filterNodeType"
                 ref="treeType" :expand-on-click-node="false" :highlight-current="true" @node-click="chooseType">
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span>{{ node.label }}</span>
                        <span class="custom-tree-node-buttons" v-if="data.id !== '0'">
                            <el-button icon="el-icon-plus" type="text" size="small"
                                       @click.stop="() => addType(data)"></el-button>
                            <el-button icon="el-icon-edit" type="text" size="small"
                                       @click.stop="() => updateType(data)"></el-button>
                            <el-button icon="el-icon-delete" type="text" size="small" style="color: red;"
                                       @click.stop="() => deleteType(data)"></el-button>
                        </span>
                    </span>
        </el-tree>
      </div>
    </div>
    <div class="drugDiameter-right">
      <div style="margin-bottom: 10px;font-weight: bold;margin-left: 5px;font-size: 16px">
        <span>当前总药径：<span style="color: #409EFF">{{ sumTotal }}</span> 条，正在运行：<span
            style="color: #409EFF">{{ inProgressTotal }}</span> 条</span>
      </div>
      <el-form :inline="true" @keyup.enter.native="getDataList()" size="small">
        <el-form-item label="药径名称：" label-width="90px">
          <el-input v-model="dataForm.drugPathwayName" placeholder="请输入药径名称" max-length="150"
                    clearable></el-input>
        </el-form-item>
        <el-form-item label="状态：" label-width="60px">
          <el-radio-group v-model="dataForm.status">
            <el-radio label="">全部</el-radio>
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item style="margin-left: 10px;">
          <el-button @click="getDataList('cx')" type="primary">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="addOrUpdateDiameter()" type="primary">新增</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="deleteHandle()" type="danger">批量删除</el-button>
        </el-form-item>
        <div class="cardOrList">
          <el-radio-group v-model="view" size="small">
            <el-radio-button label="list"><i class="el-icon-tickets"></i></el-radio-button>
            <el-radio-button label="card"><i class="el-icon-s-grid"></i></el-radio-button>
          </el-radio-group>
        </div>
      </el-form>
      <transition-group name="displayChange" appear>
        <div key="card" class="card-view-outer" v-if="view === 'card'" v-loading="dataListLoading"
             :style="{ height: heightY - topY - 50 + 'px' }">
          <div class="card-view-empty" v-if="dataList.length === 0">
            暂无数据
          </div>
          <el-checkbox-group v-else v-model="checkListCard">
            <el-checkbox class="card-view" v-for="item in dataList" :key="item.id" :label="item.id" style="position: relative;">
              <div class="published">
                <div class="text">{{item.currentState}}</div>
              </div>
              <div class="card-view-item">
                <div class="card-view-item-content" @click.prevent="infoPage(item)">
                  <div class="content-left">
                    <img src="@/assets/image/md5__af93c86f3c2a5207f11f63943c9ec781.png" alt="">
                  </div>
                  <div class="content-right">
                    <div class="content-title">
                      {{ item.drugPathwayName }}
                    </div>
                    <div class="content-dept">
                      <span>所属科室：</span>
                      <span>{{ item.deptName }}</span>
                    </div>
                    <div class="content-dept">
                      <span>所属类别：</span>
                      <span>{{ item.drugTypeName }}</span>
                    </div>
                    <!-- <div class="content-dept">
                      <span>创建时间：</span>
                      <span>{{ item.createDate }}</span>
                    </div> -->
                    <div class="content-dept">
                      <span>当前版本：</span>
                      <span>{{ item.versionNum }}</span>
                    </div>
                  </div>
                  <!-- <div class="status-box">
                    <img v-if="item.status === '0'" class="tingyong2"
                         src="@/assets/image/md5__6bab7745006fa36eb425c1f1813ba619.svg" alt="">
                    <img v-else class="tingyong"
                         src="@/assets/image/md5__ad0ac5bc67daf11c4cfb8ab74dbd2f32.svg" alt="">
                  </div> -->
                </div>
                <div class="card-view-item-buttom">
                  <div class="card-view-item-buttom-item yellow"
                       @click.prevent="feedbackHandle(item)">
                    反馈
                  </div>
                  <div v-if="infoPerm(item)" class="card-view-item-buttom-item"
                       @click.prevent="editDiameter(item)">
                    编辑
                  </div>
                  <div class="card-view-item-buttom-item green" @click.prevent="fabuFun(item)">
                    <!-- <el-badge :value="parseInt(Math.random() * 10)" :max="10" type="primary"> -->
                    <span v-if="item.isRelease === 0">发布</span>
                    <!-- </el-badge> -->
                    <span v-if="item.isRelease === 1">审核中</span>
                    <span v-if="item.isRelease === 2">已发布</span>
                  </div>
                  <div class="card-view-item-buttom-item gry" @click.prevent="mappingFun(item)">
                    匹配设置
                  </div>
                </div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <el-table key="list" v-if="view === 'list'" ref="listTable" :data="dataList"
                  @selection-change="dataListSelectionChangeHandle" row-key="id" :key="total" border stripe
                  highlight-current-row :height="heightY - topY - 50 + 'px'" style="width: 100%;margin-top: -10px"
                  size="small" v-loading="dataListLoading">
          <el-table-column align="center" header-align="center" type="selection" width="50"></el-table-column>
          <el-table-column prop="drugPathwayName" label="药径名称" header-align="center" align="center"
                           show-overflow-tooltip min-width="200"></el-table-column>
          <el-table-column prop="deptName" label="所属科室" header-align="center" align="center"
                           show-overflow-tooltip min-width="100"></el-table-column>
          <el-table-column prop="drugTypeName" label="所属类别" header-align="center" align="center"
                           show-overflow-tooltip min-width="100"></el-table-column>
          <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"
                           show-overflow-tooltip min-width="100"></el-table-column>
          <el-table-column prop="remark" label="备注" header-align="center" align="center" show-overflow-tooltip
                           min-width="100"></el-table-column>
          <el-table-column prop="status" label="状态" header-align="center" align="center" min-width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === '0'" size="small">正常</el-tag>
              <el-tag v-else size="small" type="danger">停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" label="操作" width="230">
            <template slot-scope="scope">
              <el-button @click="feedbackHandle(scope.row)" size="small" type="text">反馈</el-button>
              <el-button @click="infoPage(scope.row)" size="small" type="text">查看</el-button>
              <el-button v-if="infoPerm(scope.row)" @click="editDiameter(scope.row)" size="small"
                         type="text">编辑
              </el-button>
              <el-button @click="fabuFun(scope.row)" size="small" type="text">
                <span v-if="scope.row.isRelease === 0">发布</span>
                <span style="color:gray;" v-if="scope.row.isRelease === 1">审核中</span>
                <span style="color:gray;" v-if="scope.row.isRelease === 2">已发布</span>
              </el-button>
              <el-button @click="mappingFun(scope.row)" size="small" type="text">匹配设置</el-button>
            </template>
          </el-table-column>
        </el-table>
      </transition-group>
      <!-- <el-pagination style="margin: 5px 0px -15px" :current-page="page" :page-size="limit"
                     :page-sizes="[5, 15, 20, 50, 100]" :total="total" @current-change="pageCurrentChangeHandle"
                     @size-change="pageSizeChangeHandle" layout="total, sizes, prev, pager, next, jumper">
      </el-pagination> -->
      <div style="text-align: right;">
              <el-pagination style="margin-top: 20px" background :current-page="page" :page-size="limit"
                    :page-sizes="[10, 20, 50, 100]" :total="total" @current-change="pageCurrentChangeHandle"
                    @size-change="pageSizeChangeHandle"
                    layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
            </div>
      <!-- 新增或修改类型 -->
      <addOrUpdateType @onUpdateSuccess="initLeftTypeTreeData" ref="addOrUpdateType"></addOrUpdateType>
      <!-- 新增或者修改药径 -->
      <addOIrUpdateDiameter ref="addOIrUpdateDiameter" @refreshList="getDataList"></addOIrUpdateDiameter>
      <addOIrUpdateDiameterinfo ref="addOIrUpdateDiameterinfo" @refreshList="getDataList">
      </addOIrUpdateDiameterinfo>
      <!-- 反馈 -->
      <feedback ref="feedback" @refreshList="getDataList"></feedback>
      <!-- 匹配 -->
      <autoMapping ref="autoMapping" @refreshList="getDataList"></autoMapping>
      <!-- 发布人员选择 -->
      <qz-user-select-dialog ref="Userchoose" :ccPerson="ccPerson" :multiple="false" :maxSelect="1"
                             :allowGroup="false" @onOk="handleUserChooseCallback"></qz-user-select-dialog>
    </div>
  </div>
</template>

<script>
/**
 * @name drugDiameter (组件名称)
 * @module 组件存放位置
 * @desc 组件描述
 * <AUTHOR>
 * @date
 * @param {Object} [title]    - 参数说明
 * @param {String} [columns] - 参数说明
 * @example 调用示例
 *  <drugDiameter></drugDiameter>
 */
import util from '@/libs/util'
import resizemix from '@/components/qz-mixins/windowresize/resizemix'
import addOrUpdateType from '@/platform/workflow/bussiness/actextbussiness-add-or-update.vue'
import addOIrUpdateDiameter from './addOrUpdate.vue'
import addOIrUpdateDiameterinfo from './info.vue'
import feedback from './feedback.vue'
import autoMapping from './autoMapping/index.vue'
import * as api from './api'
import { mapState } from 'vuex'

export default {
  name: 'drugDiameter',
  mixins: [resizemix],
  components: { addOrUpdateType, addOIrUpdateDiameter, feedback, addOIrUpdateDiameterinfo, autoMapping },
  props: {},
  data () {
    return {
      view: 'card',
      filterTextDept: '',
      filterTextType: '',
      dataDept: [],
      dataType: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultPropsType: {
        children: 'children',
        label: 'nodeName'
      },
      dataForm: {
        drugPathwayName: '',
        status: ''
      },
      leftTreeFilter: {
        deptId: '',
        deptName: '',
        drugTypeId: '',
        drugTypeName: ''
      },
      dataList: [],
      page: 1,
      limit: 20,
      total: 0,
      dataListLoading: false,
      dataListSelections: [],
      checkListCard: [],
      ccPerson: '',
      fabuCurrItem: {},
      inProgressTotal: 0,
      sumTotal: 0
    }
  },
  created () {
    this.initLeftDeptTreeData()
    this.initLeftTypeTreeData()
    this.getDataList()
  },
  mounted () {
  },
  methods: {
    // 编辑按钮权限
    infoPerm (item) {
      return item.creator == this.info.id &&
          (item.isRelease === 0 || item.isRelease === 2)
    },
    fabuFun (item) {
      if (item.isRelease !== 0) {
        return
      }
      if (item.status === '1' || item.status === 1) {
        this.$message.warning('药径状态为未启用，请先启用药径之后再发布')
        return
      }
      this.fabuCurrItem = item
      item.type = 'fb'
      api.validateBpmnModel(item).then((res) => {
        if (!res) {
          this.$message.warning('药径配置校验未通过，请检查药径配置之后再重新发布药径')
        } else {
          this.$nextTick(() => {
            this.$refs.Userchoose.init()
          })
        }
      })
    },
    handleUserChooseCallback (finalUsers) {
      this.ccPerson = ''
      // 提示用户是否确认
      if (finalUsers.length > 0) {
        this.$confirm(`药径将提交给【${finalUsers[0].name}】审核，是否确认?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dataListLoading = true
          api.initiateExamine({
            id: this.fabuCurrItem.id,
            examiner: finalUsers[0].id
          }).then(() => {
            this.dataListLoading = false
            this.getDataList()
            this.$message({
              type: 'success',
              message: '已提交审核!'
            })
          }).catch(() => {
            this.dataListLoading = false
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      }
    },
    chooseDept (data) {
      this.leftTreeFilter.deptId = data.pkid
      this.leftTreeFilter.deptName = data.name
      this.getDataList()
    },
    chooseType (data) {
      this.leftTreeFilter.drugTypeId = data.id
      this.leftTreeFilter.drugTypeName = data.nodeName
      this.getDataList()
    },
    feedbackHandle (item) {
      this.$refs.feedback.init(item.id)
    },
    mappingFun (item) {
      this.$refs.autoMapping.init(item.id)
    },
    editDiameter (item) {
      this.$refs.addOIrUpdateDiameter.init(item.id)
    },
    infoPage (item) {
      this.$refs.addOIrUpdateDiameterinfo.init(item.id)
    },
    addOrUpdateDiameter () {
      // 机构、类型默认赋值
      if (this.leftTreeFilter.deptId && this.leftTreeFilter.deptName) {
        this.$refs.addOIrUpdateDiameter.dataform.deptId = this.leftTreeFilter.deptId
        this.$refs.addOIrUpdateDiameter.dataform.deptName = this.leftTreeFilter.deptName
      }
      if (this.leftTreeFilter.drugTypeId && this.leftTreeFilter.drugTypeName) {
        this.$refs.addOIrUpdateDiameter.dataform.drugTypeId = this.leftTreeFilter.drugTypeId
        this.$refs.addOIrUpdateDiameter.dataform.drugTypeName = this.leftTreeFilter.drugTypeName
      }
      this.$refs.addOIrUpdateDiameter.init()
    },
    changeView (view) {
      this.view = view
    },
    getDataList (val) {
      if (val === 'cx') {
        this.page = 1
      }
      const params = {
        page: this.page,
        limit: this.limit,
        ...this.dataForm,
        deptId: this.leftTreeFilter.deptId,
        drugTypeId: this.leftTreeFilter.drugTypeId
      }
      this.indexStart = (this.page - 1) * this.limit + 1
      this.dataListLoading = true
      api.page(params).then((data) => {
        data.records = data.records.map(item => {
          item.status = item.status + ''
          return item
        })
        this.dataList = data.records
        this.total = data.total * 1
        if (this.total > 0 && data.records.length == 0) {
          const count = Math.ceil(this.total / this.limit)
          this.page = count
          this.getDataList()
          return
        }
        this.dataListLoading = false
        this.getTotal()
      }).catch((err) => {
        this.dataListLoading = false
        util.$message.showInfo2(err)
      })
    },
    pageSizeChangeHandle (val) {
      this.page = 1
      this.limit = val
      this.getDataList()
    },
    pageCurrentChangeHandle (val) {
      this.page = val
      this.getDataList()
    },
    dataListSelectionChangeHandle (val) {
      this.dataListSelections = val
    },
    async deleteHandle (row) {
      let delIds = []
      if (this.view === 'card') {
        // 卡片模式下删除
        if (this.checkListCard.length <= 0) {
          return util.$message.showInfo2('请勾选删除项卡片')
        }
        delIds = this.checkListCard
      } else {
        // 列表模式下删除
        if (!row && this.dataListSelections.length <= 0) {
          return util.$message.showInfo2('请选择列表删除项')
        }
        delIds = this.dataListSelections.map(item => item.id)
      }
      const ok = await util.$message.showYesNo('确定删除所选数据项?')
      if (!(ok && ok === 'confirm')) {
        return ''
      }
      await api.deleteFun(delIds)
      this.getDataList()
    },
    initLeftDeptTreeData () {
      api.sysDeptTree('001001002').then((res) => {
        this.dataDept = res
      })
    },
    initLeftTypeTreeData (id) {
      this.dataType = []
      const rootNode = {
        id: '0',
        nodeName: '全部'
      }
      api.select_bussiness_tree().then((res) => {
        rootNode.children = res
        this.dataType.push(rootNode)
      })
      if (this.leftTreeFilter.drugTypeId === id) {
        this.getDataList()
      }
    },
    // 增加
    addTypeTop () {
      this.$refs.addOrUpdateType.dataForm.id = ''
      this.$refs.addOrUpdateType.init(0, '父级药径', '0')
    },
    addType (curNode) {
      this.$refs.addOrUpdateType.dataForm.id = ''
      this.$refs.addOrUpdateType.init(curNode.id, curNode.nodeName, curNode.nodeLevel)
    },
    // 修改
    updateType (curNode) {
      const id = curNode.id
      this.$nextTick(() => {
        if (!id) {
          return
        }
        this.$refs.addOrUpdateType.dataForm.id = id
        this.$refs.addOrUpdateType.init()
      })
    },
    deleteType (curNode) {
      const id = curNode.id
      if (!id) {
        return
      }
      this.$confirm('此操作将永久删除该药径类别, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.delete_bussiness_tree(id).then((res) => {
          if (!res) {
            this.$message({
              type: 'warning',
              message: '该药径类别下存在药径数据，不允许删除!'
            })
          } else {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.initLeftTypeTreeData()
            if (this.leftTreeFilter.drugTypeId === id) {
              this.leftTreeFilter.drugTypeId = '0'
              this.leftTreeFilter.drugTypeName = '全部'
              this.getDataList()
            }
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    filterNodeDept (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    filterNodeType (value, data) {
      if (!value) return true
      return data.nodeName.indexOf(value) !== -1
    },
    getTotal () {
      this.getDataListTotal()
      this.getDataListInProgress()
    },
    // 总数量的查询
    getDataListTotal () {
      const params = {
        page: this.page,
        limit: this.limit,
        deptId: '',
        drugTypeId: ''
      }
      api.page(params).then((data) => {
        this.sumTotal = data.total
      }).catch((err) => {
        util.$message.showInfo2(err)
      })
    },
    // 正在运行的查询
    getDataListInProgress () {
      const params = {
        page: this.page,
        limit: this.limit,
        deptId: '',
        drugTypeId: '',
        isRelease: '2'
      }
      api.page(params).then((data) => {
        this.inProgressTotal = data.total
      }).catch((err) => {
        util.$message.showInfo2(err)
      })
    }
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ])
  },
  watch: {
    filterTextDept (val) {
      this.$refs.treeDept.filter(val)
    },
    filterTextType (val) {
      this.$refs.treeType.filter(val)
    }
  }
}
</script>
<style scoped lang="scss">
@import url(~@/assets/font/font.css);
</style>
<style lang="scss" scoped>
.drugDiameter {
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;

  div {
    box-sizing: border-box;
    flex-shrink: 0 !important;
    min-width: 0;
    min-height: 0;
  }

  * {
    font-family: 'PingFang-SC', 'Microsoft YaHei', sans-serif;
  }

  .drugDiameter-left {
    flex: 2.5;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .filter-left-top-box {
      flex: 1;
      width: 100%;
      height: 50%;
      border-bottom: 1px solid #DCDFE6;
    }

    .filter-left-bottom-box {
      flex: 1;
      width: 100%;
      height: 50%;
      padding-top: 7px;

      ::v-deep .el-tree {
        .custom-tree-node {
          .custom-tree-node-buttons {
            margin-left: 10px;
            display: none;
          }
        }

        .el-tree-node__content:hover {
          .custom-tree-node-buttons {
            display: inline-block;
          }
        }

        // .is-current>.el-tree-node__content {
        //     .custom-tree-node-buttons {
        //         display: inline-block;
        //     }
        // }
      }
    }

    .filter-title {
      font-size: 16px;
      font-weight: 700;
      position: relative;
      padding-left: 10px;
      margin-bottom: 5px;

      &::before {
        content: '';
        display: inline-block;
        width: 5px;
        height: 18px;
        background-color: #409EFF;
        margin-right: 5px;
        position: absolute;
        left: 0px;
        top: 3px;
      }
    }
  }

  .drugDiameter-right {
    flex: 10;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    position: relative;

    .cardOrList {
      position: absolute;
      right: 10px;
      top: 10px;
    }

    .card-view-outer {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      overflow: auto;
      padding: 10px;
      margin-top: -10px;
      padding-top: 0;

      .card-view-empty {
        width: 100%;
        text-align: center;
        font-size: 16px;
        color: #909399;
        padding-top: 20vh;
      }

      ::v-deep .el-checkbox-group {
        height: 100%;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 2%;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 10px;
        align-items: flex-start;
        align-content: flex-start;
        justify-content: flex-start;

        .el-checkbox__input {
          position: relative;
          top: -16px;
        }

        .el-checkbox__label {
          width: calc(100% - 9px);
        }

        .card-view {
          // height: 150px;
          width: 23.5%;
          margin-right: 0;
          border-radius: 5px;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .13);
          padding: 10px;
          transition: all .3s ease;
          border: 1px solid #ebeef5;
          position: relative;
          padding-bottom: 37px;

          &:hover {
            border: 1px solid rgba(77, 159, 242, .61);
            transform: scale(1.04);
            box-shadow: 0 2px 12px 0 rgba(77, 159, 242, .31);
          }
.published{
    position: absolute;
    right: 0;
    top: 0;
    width: 60px;  /* 图标宽度 */
    height: 60px; /* 图标高度 */
    background-image: url("../components/img/status.png"); /* 替换为你的图片URL */
  background-size: 100% 100%;    /* 保持比例并覆盖整个区域 */
  background-repeat: no-repeat;
  .text{
    position: absolute;
  top: 36%;
  left: 64%;
  transform: translate(-50%, -50%) rotate(45deg); /* 居中+旋转 */
  color: white; /* 文字颜色根据图片调整 */
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  width: max-content; /* 自适应文字宽度 */
  }
  }

          .card-view-item {
            .card-view-item-content {
              display: flex;
              align-items: center;
              gap: 10px;

              .content-left {
                img {
                  width: 45px;
                  height: 45px;
                }
              }

              .content-right {
                width: calc(100% - 55px);

                .content-title {
                  width: calc(100% - 30px);
                  font-size: 16px;
                  font-weight: 700;
                  padding: 5px 0;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .content-dept {
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  font-size: 14px;
                  padding-top: 3px;

                  &:nth-last-child(1) {
                    padding-bottom: 12px;
                  }
                }

              }

              .status-box {
                position: absolute;
                top: 10px;
                right: 10px;

                .tingyong2 {
                  width: 25px;
                  height: 25px;
                }

                .tingyong {
                  width: 23px;
                  height: 23px;
                }
              }
            }

            .card-view-item-buttom {
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 37px;
              line-height: 37px;
              border-top: 1px solid #DCDFE6;
              display: flex;
              justify-content: space-around;
              align-items: center;

              .card-view-item-buttom-item {
                font-size: 14px;
                text-align: center;
                color: #4BA4FF;
                flex: 1;
                border-right: 1px solid #DCDFE6;
                cursor: pointer;

                &:last-child {
                  border-right: none;
                }
              }

              .yellow {
                color: #E6A23C;
              }

              .green {
                color: #67C23A;

                .el-badge__content.is-fixed {
                  top: 11px;
                  right: -2px;
                  background-color: #67C23A;
                }
              }

              .gry {
                color: #909399;
              }
            }

          }
        }
      }
    }
  }
}

.displayChange-enter-active {
  animation: run-scale .7s ease 0s;
}

@keyframes run-scale {
  0% {
    transform: translate(20px, 20px) scale(0.9);
    opacity: 0;
  }
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }
}
</style>
