<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选诊断" prop="choosedDiagnosis">
          <div class="tag-container">
            <div class="tag-scroll-container">
              <el-tag v-for="(diagnosis, index) in choosedDiagnosisShow" :key="index" closable
                @close="handleRemoveTag(index)" style="margin: 2px 5px 2px 0;">
                {{ diagnosis }}
              </el-tag>
            </div>
            <div v-if="choosedDiagnosisShow.length === 0" class="el-form-item__error">
              请选择诊断
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入诊断" prop="diagnosis">
          <el-input placeholder="输入关键字过滤诊断" v-model="filterTextDiagnosis" size="small">
            <i @click="initLeftDiagnosisTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataDiagnosis" show-checkbox node-key="id" :props="defaultProps"
            default-expand-all style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeDiagnosis"
            ref="treeDiagnosis" :expand-on-click-node="false" :highlight-current="true"
            @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
        }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
import request from '@/plugin/axios'

export default {
  name: 'diagnosis-select',
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '选择诊断',
      filterTextDiagnosis: '',
      dataDiagnosis: [],
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      dataForm: {
        choosedDiagnosis: []
      },
      localSelectedDiagnoses: []
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.dataForm.choosedDiagnosis = []
      this.filterTextDiagnosis = ''
      this.dataDiagnosis = []
      //初始化本地已选择的诊断
      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')
        const names = this.selectedName.split(',')

        this.localSelectedDiagnoses = codes.map((code, index) => ({
          id: code,
          diagnosisCode: code,
          diseaseName: names[index]
        }))
      } else {
        this.localSelectedDiagnoses = []
      }

      this.$nextTick(() => {
        this.initLeftDiagnosisTreeData()
      })
    },

    initData(data) {
      this.$refs.treeDiagnosis.setCheckedKeys(data.split(','))
    },

    getParams() {
      return this.dataForm.choosedDiagnosis.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => {
      })
    },
    handleCheckChange(checkedNode, isChecked) {
      // 检查是否为父节点（有子节点）
      const isParentNode = checkedNode.children && checkedNode.children.length > 0;

      if (isChecked) {
        if (isParentNode) {
          // 如果是父节点，不添加父节点本身，而是添加所有子节点
          this.addChildrenNodes(checkedNode);
        } else {
          // 如果是叶子节点，检查是否已存在（避免重复添加）
          this.addSingleNode(checkedNode);
        }
      } else {
        if (isParentNode) {
          // 如果取消勾选父节点，移除所有子节点
          this.removeChildrenNodes(checkedNode);
        } else {
          // 取消勾选叶子节点 - 只移除这个具体的节点
          this.removeSingleNode(checkedNode);
        }
      }
    },

    // 添加单个节点
    addSingleNode(node) {
      // 检查是否已存在于本地已选中（通过diagnosisCode）
      const existingInLocal = this.localSelectedDiagnoses.some(s => s.diagnosisCode === node.diagnosisCode);
      // 检查是否已存在于新选择中（通过id）
      const existingInNew = this.dataForm.choosedDiagnosis.some(s => s.id === node.id);

      if (!existingInLocal && !existingInNew) {
        this.dataForm.choosedDiagnosis.push(node);
      }
    },

    // 移除单个节点
    removeSingleNode(node) {
      // 只从新选择的列表中移除，绝对不触碰本地已选
      this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(s => s.id !== node.id);
    },

    // 添加所有子节点到已选列表
    addChildrenNodes(parentNode) {
      const addNode = (node) => {
        if (node.children && node.children.length > 0) {
          // 如果还有子节点，递归处理
          node.children.forEach(child => addNode(child));
        } else {
          // 叶子节点，使用统一的添加方法
          this.addSingleNode(node);
        }
      };
      addNode(parentNode);
    },

    // 移除所有子节点从已选列表
    removeChildrenNodes(parentNode) {
      const removeNode = (node) => {
        if (node.children && node.children.length > 0) {
          // 如果还有子节点，递归处理
          node.children.forEach(child => removeNode(child));
        } else {
          // 叶子节点，使用统一的移除方法
          this.removeSingleNode(node);
        }
      };
      removeNode(parentNode);
    },
    initLeftDiagnosisTreeData() {
      if (this.filterTextDiagnosis) {
        request({
          url: 'business/busdiseasetypemanage/listTree',
          method: 'get',
          params: { diseaseName: this.filterTextDiagnosis }
        }).then((res) => {
          this.dataDiagnosis = res
          const selectedCodes = this.localSelectedDiagnoses.map(s => s.diagnosisCode)
          const checkedIds = []
          const traverse = (nodes) => {
            for (const node of nodes) {
              if (selectedCodes.includes(node.diagnosisCode)) {
                checkedIds.push(node.id)
              }
              if (node.children && node.children.length > 0) {
                traverse(node.children)
              }
            }
          }
          traverse(this.dataDiagnosis)
          this.$refs.treeDiagnosis.setCheckedKeys(checkedIds)
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },

    filterNodeDiagnosis(value, data) {
      if (!value) return true
      return data.diseaseName.indexOf(value) !== -1
    },

    handleRemoveTag(index) {
      const totalSelected = this.localSelectedDiagnoses.length
      if (index < totalSelected) {
        // 删除本地已选项目
        const removedDiagnosis = this.localSelectedDiagnoses[index]
        this.localSelectedDiagnoses.splice(index, 1)

        // 取消树中对应节点的选中状态
        if (this.$refs.treeDiagnosis) {
          // 查找所有匹配的节点，但只取消选中叶子节点
          const matchingNodes = this.findAllNodesByDiagnosisCode(this.dataDiagnosis, removedDiagnosis.diagnosisCode)
          matchingNodes.forEach(node => {
            // 只取消选中叶子节点（没有子节点的节点）
            if (!node.children || node.children.length === 0) {
              this.$refs.treeDiagnosis.setChecked(node.id, false)
            }
          })
        }
      } else {
        // 删除新选择的项目
        const newIndex = index - totalSelected
        const removedDiagnosis = this.dataForm.choosedDiagnosis[newIndex]
        this.dataForm.choosedDiagnosis.splice(newIndex, 1)
        if (this.$refs.treeDiagnosis && removedDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(removedDiagnosis.id, false)
        }
      }
    },

    findNodeByDiagnosisCode(nodes, diagnosisCode) {
      for (const node of nodes) {
        if (node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByDiagnosisCode(node.children, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    // 查找所有匹配diagnosisCode的节点
    findAllNodesByDiagnosisCode(nodes, diagnosisCode) {
      const matchingNodes = []
      const traverse = (nodeList) => {
        for (const node of nodeList) {
          if (node.diagnosisCode === diagnosisCode) {
            matchingNodes.push(node)
          }
          if (node.children && node.children.length) {
            traverse(node.children)
          }
        }
      }
      traverse(nodes)
      return matchingNodes
    },

    async dataFormSubmitHandle() {
      if (this.choosedDiagnosisShow.length === 0) {
        this.$message.error('请选择诊断')
        return
      }
      this.loading = true
      try {
        const allDiagnoses = [
          ...this.localSelectedDiagnoses,
          ...this.dataForm.choosedDiagnosis
        ]
        const finalDiagnosisCode = allDiagnoses.map(item => item.diagnosisCode).join(',')
        const finalDiseaseName = allDiagnoses.map(item => item.diseaseName).join(',')

        this.$emit('callback', {
          diagnosisCode: finalDiagnosisCode,
          diseaseName: finalDiseaseName
        })
        this.visible = false
      } catch (err) {
        console.error(err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedDiagnosisShow() {
      // 如果有已选的诊断，显示已选的和新选的组合，否则显示传入的selectedName
      const existingNames = this.localSelectedDiagnoses.map(v => v.diseaseName)
      const newNames = this.dataForm.choosedDiagnosis.map(v => v.diseaseName)
      return [...existingNames, ...newNames]
    },
    choosedDiagnosisCode() {
      return this.dataForm.choosedDiagnosis.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedDiagnosis.map(v => v.diagnosisCode).join(',')
        : this.selectedCode
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  margin-bottom: 5px;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
  max-height: 150px;
}

.tag-container-wrapper {
  position: relative;
}

.tag-scroll-container {
  max-height: 120px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.tag-scroll-container .el-tag {
  margin: 4px 6px 4px 0;
}
</style>