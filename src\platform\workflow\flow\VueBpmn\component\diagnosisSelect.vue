<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选诊断" prop="choosedDiagnosis">
          <div class="tag-container">
            <div class="tag-scroll-container">
              <el-tag v-for="(diagnosis, index) in choosedDiagnosisShow" :key="index" closable
                @close="handleRemoveTag(index)" style="margin: 2px 5px 2px 0;">
                {{ diagnosis }}
              </el-tag>
            </div>
            <div v-if="choosedDiagnosisShow.length === 0" class="el-form-item__error">
              请选择诊断
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入诊断" prop="diagnosis">
          <el-input placeholder="输入关键字过滤诊断" v-model="filterTextDiagnosis" size="small">
            <i @click="initLeftDiagnosisTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataDiagnosis" show-checkbox node-key="id" :props="defaultProps"
            default-expand-all style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeDiagnosis"
            ref="treeDiagnosis" :expand-on-click-node="false" :highlight-current="true"
            @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
        }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
import request from '@/plugin/axios'

export default {
  name: 'diagnosis-select',
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '选择诊断',
      filterTextDiagnosis: '',
      dataDiagnosis: [],
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      // 本地已选的诊断（通过props传入，不可通过树操作修改）
      localSelectedDiagnoses: [],
      // 新选择的诊断（通过树操作添加/删除）
      newSelectedDiagnoses: []
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.newSelectedDiagnoses = []
      this.filterTextDiagnosis = ''
      this.dataDiagnosis = []

      // 初始化本地已选择的诊断
      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')
        const names = this.selectedName.split(',')

        this.localSelectedDiagnoses = codes.map((code, index) => ({
          diagnosisCode: code,
          diseaseName: names[index],
          isLocal: true // 标记为本地已选项目
        }))
      } else {
        this.localSelectedDiagnoses = []
      }

      this.$nextTick(() => {
        this.initLeftDiagnosisTreeData()
      })
    },

    initData(data) {
      this.$refs.treeDiagnosis.setCheckedKeys(data.split(','))
    },

    getParams() {
      return this.newSelectedDiagnoses.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => {
      })
    },
    handleCheckChange(checkedNode, isChecked) {
      // 延迟执行，让树的状态先更新
      this.$nextTick(() => {
        this.syncTreeSelection();
      });
    },

    // 同步树的选择状态到数据
    syncTreeSelection() {
      if (!this.$refs.treeDiagnosis) return;

      // 获取当前树中所有选中的叶子节点
      const checkedNodes = this.$refs.treeDiagnosis.getCheckedNodes();
      const checkedLeafNodes = checkedNodes.filter(node =>
        !node.children || node.children.length === 0
      );

      // 重新构建新选择列表
      this.newSelectedDiagnoses = [];

      checkedLeafNodes.forEach(node => {
        // 检查是否已在本地已选中
        const isInLocal = this.localSelectedDiagnoses.some(item =>
          item.diagnosisCode === node.diagnosisCode
        );

        // 如果不在本地已选中，则添加到新选择列表
        if (!isInLocal) {
          this.newSelectedDiagnoses.push({
            id: node.id,
            diagnosisCode: node.diagnosisCode,
            diseaseName: node.diseaseName,
            isLocal: false
          });
        }
      });
    },
    initLeftDiagnosisTreeData() {
      if (this.filterTextDiagnosis) {
        this.loading = true;
        request({
          url: 'business/busdiseasetypemanage/listTree',
          method: 'get',
          params: { diseaseName: this.filterTextDiagnosis }
        }).then((res) => {
          this.dataDiagnosis = res;

          // 设置本地已选项目的选中状态
          this.setInitialCheckedState();

          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      }
    },

    // 设置初始选中状态
    setInitialCheckedState() {
      if (!this.$refs.treeDiagnosis) return;

      const selectedCodes = this.localSelectedDiagnoses.map(item => item.diagnosisCode);
      const checkedIds = [];

      const traverse = (nodes) => {
        for (const node of nodes) {
          // 如果是叶子节点且在本地已选中，则标记为选中
          if ((!node.children || node.children.length === 0) &&
              selectedCodes.includes(node.diagnosisCode)) {
            checkedIds.push(node.id);
          }

          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        }
      };

      traverse(this.dataDiagnosis);
      this.$refs.treeDiagnosis.setCheckedKeys(checkedIds);
    },

    filterNodeDiagnosis(value, data) {
      if (!value) return true
      return data.diseaseName.indexOf(value) !== -1
    },

    handleRemoveTag(index) {
      const totalLocal = this.localSelectedDiagnoses.length;

      if (index < totalLocal) {
        // 删除本地已选项目
        const removedItem = this.localSelectedDiagnoses[index];
        this.localSelectedDiagnoses.splice(index, 1);

        // 取消树中对应节点的选中状态
        this.uncheckTreeNodesByCode(removedItem.diagnosisCode);
      } else {
        // 删除新选择的项目
        const newIndex = index - totalLocal;
        const removedItem = this.newSelectedDiagnoses[newIndex];

        // 直接从新选择列表中移除
        this.newSelectedDiagnoses.splice(newIndex, 1);

        // 取消树中对应节点的选中状态
        if (this.$refs.treeDiagnosis && removedItem) {
          this.$refs.treeDiagnosis.setChecked(removedItem.id, false);
          // 触发同步，确保状态一致
          this.$nextTick(() => {
            this.syncTreeSelection();
          });
        }
      }
    },

    // 根据诊断编码取消选中树节点
    uncheckTreeNodesByCode(diagnosisCode) {
      if (!this.$refs.treeDiagnosis) return;

      const matchingNodes = this.findAllNodesByDiagnosisCode(this.dataDiagnosis, diagnosisCode);
      matchingNodes.forEach(node => {
        // 只取消选中叶子节点
        if (!node.children || node.children.length === 0) {
          this.$refs.treeDiagnosis.setChecked(node.id, false);
        }
      });

      // 触发同步
      this.$nextTick(() => {
        this.syncTreeSelection();
      });
    },

    findNodeByDiagnosisCode(nodes, diagnosisCode) {
      for (const node of nodes) {
        if (node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByDiagnosisCode(node.children, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    // 查找所有匹配diagnosisCode的节点
    findAllNodesByDiagnosisCode(nodes, diagnosisCode) {
      const matchingNodes = []
      const traverse = (nodeList) => {
        for (const node of nodeList) {
          if (node.diagnosisCode === diagnosisCode) {
            matchingNodes.push(node)
          }
          if (node.children && node.children.length) {
            traverse(node.children)
          }
        }
      }
      traverse(nodes)
      return matchingNodes
    },

    async dataFormSubmitHandle() {
      if (this.allSelectedDiagnoses.length === 0) {
        this.$message.error('请选择诊断')
        return
      }

      this.loading = true
      try {
        const finalDiagnosisCode = this.allSelectedDiagnoses.map(item => item.diagnosisCode).join(',')
        const finalDiseaseName = this.allSelectedDiagnoses.map(item => item.diseaseName).join(',')

        this.$emit('callback', {
          diagnosisCode: finalDiagnosisCode,
          diseaseName: finalDiseaseName
        })
        this.visible = false
      } catch (err) {
        console.error('提交失败:', err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    // 所有已选择的诊断（本地已选 + 新选择）
    allSelectedDiagnoses() {
      return [...this.localSelectedDiagnoses, ...this.newSelectedDiagnoses];
    },

    // 用于显示的诊断名称列表
    choosedDiagnosisShow() {
      return this.allSelectedDiagnoses.map(item => item.diseaseName);
    },

    // 用于兼容的诊断编码
    choosedDiagnosisCode() {
      return this.allSelectedDiagnoses.map(item => item.diagnosisCode).join(',');
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  margin-bottom: 5px;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
  max-height: 150px;
}

.tag-container-wrapper {
  position: relative;
}

.tag-scroll-container {
  max-height: 120px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.tag-scroll-container .el-tag {
  margin: 4px 6px 4px 0;
}
</style>