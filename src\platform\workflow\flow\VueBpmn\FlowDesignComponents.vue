<template>
  <div>
    <el-main class="fm2-main" style="height: 100%">
      <el-container style="height: 100%">
        <el-container class="center-container" direction="vertical" style="height: 100%">
          <el-header class="btn-bar" style="height: 45px;text-align:right;line-height:45px;">
            <el-button type="text" size="medium" icon="el-icon-upload2" @click="handleImportXmlAction">导入BPMN
            </el-button>
            <input ref="file" type="file" style="display: none" @change="showRealPath">
            <el-button type="text" size="medium" icon="el-icon-download" @click="handleExportXmlAction">导出XML
            </el-button>
            <el-button type="text" size="medium" icon="el-icon-download" @click="handleExportSvgAction">导出SVG
            </el-button>
            <el-button type="text" size="medium" icon="el-icon-tickets" @click="xmlVisible = !xmlVisible">预览
            </el-button>
            <el-button type="text" size="medium" icon="el-icon-brush" @click="handleClear">清除</el-button>
            <el-button type="text" size="medium" icon="el-icon-refresh-left" @click="undo">撤销</el-button>
            <el-button type="text" size="medium" icon="el-icon-refresh-right" @click="redo">恢复</el-button>
            <el-button type="text" size="medium" icon="el-icon-zoom-in" @click="bpmnZoom(0.1)">放大</el-button>
            <el-button type="text" size="medium" icon="el-icon-zoom-out" @click="bpmnZoom(-0.1)">缩小</el-button>
            <el-button type="text" size="medium" icon="el-icon-help" @click="resetSize">定位</el-button>
            <!-- <el-button type="text" size="medium" icon="el-icon-folder-checked" @click="showSubmitXml">保存</el-button> -->
            <el-button type="text" size="medium" icon="el-icon-refresh" @click="checkDrugDiameter ">药径检查</el-button>
          </el-header>
          <el-main style="padding:5px;height:100%">
            <div class="containers" style="height: 100%">
              <div class="canvas" ref="canvas" style="height:100%"/>
              <!--预览xml-->
              <el-dialog :visible.sync="xmlVisible" title="XML" :fullscreen="false" top="10vh" :append-to-body="true">
                <vue-ace-editor v-model="process.xml" @init="editorInit" lang="xml" theme="chrome" width="100%"
                                height="400" :options="{wrap: true, readOnly: true}"/>
                <span slot="footer" class="dialog-footer">
<!--                    <el-button icon="el-icon-document" v-clipboard:copy="process.xml" v-clipboard:success="onCopy">复 制</el-button>-->
                    <el-button icon="el-icon-close" type="primary" @click="xmlVisible = false">关闭</el-button>
                  </span>
              </el-dialog>
            </div>
          </el-main>
        </el-container>
        <!--右侧表单-->
        <el-aside class="widget-config-container" style="width:380px">
          <el-container>
            <panel ref="propertyPanel" v-if="bpmnModeler" :modeler="bpmnModeler"/>
          </el-container>
        </el-aside>
      </el-container>
    </el-main>
  </div>
</template>

<script>
import CustomModeler from './CustomModeler'
import propertiesPanelModule from './CustomPanel'
import propertiesProviderModule from './CustomPanel/lib/provider/activiti'
import activitiModdleDescriptor from './CustomDescriptor/resources/activiti'
import panel from './PropertyPanel' // 流程属性面板--maidou
// 导入我们自定义的自动布局工具
import BpmData from './BpmData' // 左侧流程属性数据存储
import VueAceEditor from 'vue2-ace-editor' // 展示xml代码工具
import translate from './translate/Translate.js' // 汉化
import * as api from '../api'
import { getDefaultXml } from './defaultXml.js' // 获取默认xml文件
export default {
  props: {
    processName: {
      type: String,
      default: '流程1567044459787'
    },
    processKey: {
      type: String,
      default: 'process1567044459787'
    },
    processDescription: {
      type: String,
      default: '描述'
    }
  },

  components: {
    panel,
    VueAceEditor
  },

  data () {
    return {
      bpmnModeler: null,
      process: {
        name: this.processName,
        id: this.processKey,
        description: this.processDescription,
        xml: '',
        svg: ''
      },
      // 模型ID数据
      modelId: '',
      // 模型绑定的表单ID数据
      bussinessId: '',
      configTab: 'node',
      nodeProcessSelect: null,
      xmlVisible: false,
      element: null,
      bpmData: new BpmData(),
      title: '固定流流程设计器',
      visible: false,
      elementSelector: []
    }
  },

  methods: {
    handleClose () {
      this.clearPanels()
    },
    /**
     * 初始化组件
     */
    handleOpen () {
      const that = this
      if (!this.bpmnModeler) {
        const canvas = this.$refs.canvas
        // 汉化翻译
        var customTranslate = {
          translate: ['value', translate]
        }

        // 生成实例
        this.bpmnModeler = new CustomModeler({
          container: canvas,
          // 绑定键盘事件
          keyboard: {
            bindTo: window
          },
          // 修改元素默认颜色
          bpmnRenderer: {
            defaultFillColor: '#FFF',
            defaultStrokeColor: '#4a79ff'
          },
          additionalModules: [
            customTranslate,
            propertiesPanelModule,
            propertiesProviderModule
            // minimapModule
          ],
          // 因为原来是支持Camunda的，扩展activiti
          moddleExtensions: {
            activiti: activitiModdleDescriptor
          }
        })

        // 监听流程图改变事件
        const _this = this
        this.bpmnModeler.on('commandStack.changed', () => {
          _this.bpmnModeler.saveSVG({ format: true }, function (err, svg) {
            _this.setEncoded('SVG', err ? null : svg)
          })
          _this.bpmnModeler.saveXML({ format: true }, function (err, xml) {
            _this.setEncoded('XML', err ? null : xml)
          })
        })
      }
      this.createNewDiagram()
      this.$nextTick(() => {
        that.$refs.propertyPanel.bussinessId = that.bussinessId
      })
      this.initEvent()
    },
    /**
     * 绑定 SVG 元素高度.
     */
    getContainerHeight () {
      return (document.body.offsetHeight - 75) + 'px'
    },
    /**
     * 初始化 ace editor.
     */
    editorInit: function () {
      require('brace/ext/language_tools') // language extension prerequsite...
      require('brace/mode/xml')// language
      require('brace/theme/chrome')
    },
    /**
     * 初始化
     */
    createNewDiagram () {
      // 初始化XML文本
      if (this.modelId) {
        api.getXML(this.modelId).then((data) => {
          this.process.xml = data
          this.$nextTick(() => {
            this.openDiagram()
          })
        })
      } else {
        this.process.xml = getDefaultXml()
        this.$nextTick(() => {
          this.openDiagram()
        })
      }
    },
    updateName (name) {
      this.$refs.propertyPanel.updateName(name)
    },
    openDiagram () {
      var that = this
      // 将字符串转换成图显示出来
      this.bpmnModeler.importXML(this.process.xml, err => {
        if (err) {
          console.error(err)
        } else {
          this.adjustPalette()
          // 格式化xml
          that.bpmnModeler.saveSVG({ format: true }, function (err, svg) {
            that.setEncoded('SVG', err ? null : svg)
          })
          that.bpmnModeler.saveXML({ format: true }, function (err, xml) {
            that.setEncoded('XML', err ? null : xml)
          })
        }
      })
    },
    // 调整左侧工具栏排版
    adjustPalette () {
      try {
        // 获取 bpmn 设计器实例
        const canvas = this.$refs.canvas
        const djsPalette = canvas.children[0].children[1].children[4]
        const djsPalStyle = {
          display: 'none',
          width: '130px',
          padding: '5px',
          background: 'white',
          left: '0px',
          top: 0,
          borderRadius: 0,
          border: '1px solid #EBEEF5',
          height: '100%'
        }
        for (var key in djsPalStyle) {
          djsPalette.style[key] = djsPalStyle[key]
        }
        const palette = djsPalette.children[0]
        const allGroups = palette.children
        // 修改控件样式
        for (var gKey in allGroups) {
          const group = allGroups[gKey]
          for (var cKey in group.children) {
            const control = group.children[cKey]
            const controlStyle = {
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'center',
              width: '100%',
              padding: '5px'
            }
            if (
              control.className &&
                control.dataset &&
                control.className.indexOf('entry') !== -1
            ) {
              const controlProps = this.bpmData.getControl(
                control.dataset.action
              )
              control.innerHTML = `<div style='font-size: 14px;font-weight:500;margin-left:15px;'>${controlProps.title}</div>`
              if (controlProps.tooltip) {
                control.title = controlProps.tooltip
              }
              for (var csKey in controlStyle) {
                control.style[csKey] = controlStyle[csKey]
              }
            }
          }
        }
      } catch (e) {
        console.log(e)
      }
    },

    // 当图发生改变的时候会调用这个函数，这个data就是图的xml
    setEncoded (type, data) {
      // 把xml转换为URI，下载要用到的
      const encodedData = encodeURIComponent(data)
      if (data) {
        if (type === 'XML') {
          // 获取到图的xml，保存就是把这个xml提交给后台
          this.process.xml = data
          return {
            filename: this.process.name + '.xml',
            href: 'data:application/bpmn20-xml;charset=UTF-8,' + encodedData,
            data: data
          }
        }
        if (type === 'SVG') {
          this.process.svg = data
          return {
            filename: this.process.name + '.svg',
            href: 'data:application/text/xml;charset=UTF-8,' + encodedData,
            data: data
          }
        }
      }
    },
    // 导入本地bpmn文件
    handleImportXmlAction () {
      this.$refs.file.dispatchEvent(new MouseEvent('click'))
    },
    showRealPath () {
      const selectedFile = this.$refs.file.files[0]
      // 后缀获取
      let suffix = ''
      try {
        const fileArr = selectedFile.name.split('.')
        suffix = fileArr[fileArr.length - 1]
      } catch (err) {
        suffix = ''
      }
      if (suffix === '' || (suffix !== 'xml' && suffix !== 'bpmn')) {
        alert('不是有效的流程文件！')
        return
      }
      // FileReader对象，h5提供的异步api，可以读取文件中的数据。
      const reader = new FileReader()
      // readAsText是个异步操作，只有等到onload时才能显示数据。
      reader.readAsText(selectedFile)
      const _this = this
      reader.onload = function (event) {
        // 当读取完成后回调这个函数,然后此时文件的内容存储到了result中,直接操作即可
        //    console.log("result", event)
        _this.process.xml = event.target.result
        _this.openDiagram(event.target.result)
      }
      // 防止选择同一个文件不执行此方法
      this.$refs.file.value = null
    },
    // 撤销
    undo () {
      this.bpmnModeler.get('commandStack').undo()
    },
    // 恢复
    redo () {
      this.bpmnModeler.get('commandStack').redo()
    },
    // 重置
    resetSize () {
      const canvas = this.bpmnModeler.get('canvas')
      canvas.zoom('fit-viewport')
    },
    // 缩放
    bpmnZoom (radio) {
      const canvas = this.bpmnModeler.get('canvas')
      canvas.zoom(canvas.zoom() + radio)
    },
    // 展示保存xml提示消息
    showSubmitXml () {
      this.$confirm('保存提示', '保存提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '保存并关闭',
        cancelButtonText: '保存'
      })
        .then(() => {
          this.doSubmit(() => {
            this.visible = false
          })
        })
        .catch(action => {
          // 只有点击保存是发起保存（区分点击叉号）
          if (action === 'cancel') {
            this.doSubmit()
          }
        })
    },
    getSaveData () {
      let xmlData
      let svgData
      this.bpmnModeler.saveXML(function (err, xml) {
        if (!err) {
          xmlData = xml
        }
      })
      this.bpmnModeler.saveSVG(function (err, svg) {
        if (!err) {
          svgData = svg
        }
      })
      if (xmlData) {
        const param = {
          diagramData: encodeURIComponent(xmlData),
          svgData: encodeURIComponent(svgData),
          modelId: this.modelId
        }
        return param
      }
      return ''
    },
    // 保存设计图数据
    async doSubmit (callBackFun) {
      let xmlData
      let svgData
      this.bpmnModeler.saveXML(function (err, xml) {
        if (!err) {
          xmlData = xml
        }
      })
      this.bpmnModeler.saveSVG(function (err, svg) {
        if (!err) {
          svgData = svg
        }
      })
      if (xmlData) {
        const param = {
          diagramData: encodeURIComponent(xmlData),
          svgData: encodeURIComponent(svgData),
          modelId: this.modelId
        }
        await api.saveXml(param).then((data) => {
          if (data) {
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500
            })
            if (callBackFun) {
              callBackFun()
            }
          }
        })
      }
    },
    /**
     * 导出BPMN XML文件
     */
    handleExportXmlAction () {
      const _this = this
      this.bpmnModeler.saveXML({ format: true }, function (err, xml) {
        if (err) {
          console.error(err)
        }
        const { filename, href } = _this.setEncoded('XML', xml)
        if (href && filename) {
          const a = document.createElement('a')
          a.download = filename // 指定下载的文件名
          a.href = href //  URL对象
          a.click() // 模拟点击
          URL.revokeObjectURL(a.href) // 释放URL 对象
        }
      })
    },
    /**
     * 导出BPMN SVG文件
     */
    handleExportSvgAction () {
      const _this = this
      this.bpmnModeler.saveSVG(function (err, svg) {
        if (err) {
          console.error(err)
        }
        const { filename, href } = _this.setEncoded('SVG', svg)
        if (href && filename) {
          const a = document.createElement('a')
          a.download = filename
          a.href = href
          a.click()
          URL.revokeObjectURL(a.href)
        }
      })
    },
    /**
     * 清空设计器内容
     */
    handleClear () {
      this.clearPanels()
      this.createNewDiagram()
    },
    /**
     * 清空右侧panel中的数据
     */
    clearPanels () {
      // 选中的监听器数组
      this.$refs.propertyPanel.listenerData = []
      // 监听器(暂存)
      this.$refs.propertyPanel.listener = {}
      // 选中的任务监听器数组
      this.$refs.propertyPanel.taskListenerData = []
      // 任务监听器（暂存）
      this.$refs.propertyPanel.taskListener = {}
      // 表单字段
      this.$refs.propertyPanel.formFieldsData = {
        formName: '',
        formId: '',
        formKey: '',
        formUrl: '',
        fieldList: []
      }
      // 表单字段（暂存）
      this.$refs.propertyPanel.formFieldsTemp = {}
      // 按钮
      this.$refs.propertyPanel.buttonsData = []
      // 按钮(暂存)
      this.$refs.propertyPanel.buttonsTemp = {}
      // 审批对象
      this.$refs.propertyPanel.approversData = {}
      // 审批对象(暂存)
      this.$refs.propertyPanel.approversTemp = {}
      // 会签属性
      this.$refs.propertyPanel.multiProp = {
        // 会签设置(8不是会签，1是并行会签，2是串行会签)
        multiUserTaskFlag: 8,
        // 会签百分比还是全票 all是全票，percent是百分比
        multiUserTaskWay: 'all',
        // 会签百分比数值
        multiUserTaskPercentValue: 100
      }
      // 会签属性(暂存)
      this.$refs.propertyPanel.multiPropTemp = {}
      this.$refs.propertyPanel.flowService = {}
      // 跳过策略
      this.$refs.propertyPanel.otherProperties.skip = []
      // 跳过策略(暂存)
      this.$refs.propertyPanel.skipValuesTemp = {}
      // 超时策略
      this.$refs.propertyPanel.otherProperties.overTime = []
      // 超时策略(暂存)
      this.$refs.propertyPanel.overTimeValuesTemp = {}
      // 流转条件
      this.$refs.propertyPanel.exclusiveSequenceTemp = {}
    },
    /**
     * 复制内容到剪切板成功回调
     */
    onCopy () {
      this.$message.success('内容复制成功')
    },
    initEvent () {
      const eventBus = this.bpmnModeler.get('eventBus')
      eventBus.on('selection.changed', e => {
        this.elementSelector = e.newSelection
      })
    },
    // left/top/right/bottom/cneter/middle
    alignElements (position) {
      if (!this.elementSelector.length) return
      const alignElements = this.bpmnModeler.get('alignElements')
      alignElements.trigger(this.elementSelector, position)
    },
    // horizontal/vertical
    distributeElements (axis) {
      if (!this.elementSelector.length) return
      const alignElements = this.bpmnModeler.get('distributeElements')
      alignElements.trigger(this.elementSelector, axis)
    },
    // 药径检查
    checkDrugDiameter () {
      api.validateBpmnModel({ diagramData: this.process.xml, type: 'jc' }).then((res) => {
        if (res) {
          this.$message.success('检查通过')
        }
      })
    }
  },

  mounted () {
  },
  watch: {
    'process.xml': {
      handler (val) {
        this.$nextTick(() => {
          // 此处可以监听xml数据变化，进行一些其他操作

        })
      }
    }
  }
}
</script>
<style lang="scss">
/*左边工具栏以及编辑节点的样式*/
@import "~bpmn-js/dist/assets/diagram-js.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css";

.containers {
  background-color: #ffffff;
  width: 100%;
  height: 100%;

  .canvas {
    width: 100%;
    height: 100%;
  }

  .panel {
    position: absolute;
    right: 0;
    top: 50px;
    width: 300px;
  }

  隐藏左下方标志
  .bjs-powered-by {
    display: none;
  }

  .toolbar {
    position: absolute;
    top: 0;
    right: 320px;
    height: 40px;
    width: 600px;
    border: 1px solid red;

    a {
      text-decoration: none;
      margin: 5px;
      color: #409eff;
    }
  }

  .djs-container {
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+') repeat !important;
  }

}

// 抽屉的header下边距少点
.custom_drawer_spe_class .el-drawer__header {
  margin-bottom: 0;
}

.djs-minimap:not(.open) .toggle:before {
  content: "\e740";
  font-family: "iconfont";
  font-size: 20px;
}

.djs-minimap.open .toggle:before {
  content: "\e85f";
  font-family: "iconfont";
  font-size: 14px;
}

.djs-minimap:not(.open) .toggle,
.djs-minimap.open .toggle {
  color: #005df7;
}

.djs-minimap:not(.open) .toggle:hover,
.djs-minimap.open .toggle:hover {
  cursor: pointer;
  color: #005df7;
}

.app-icon {
  font-size: 14px;
  padding: 5px;
  margin-right: 4px;
  width: 14px;
  height: 16px;
  cursor: pointer;
  border: 1px solid rgba(1, 1, 1, 0);

  &:hover {
    background-color: #f8f8f8;
    border: 1px solid #ccc;
  }
}
</style>
