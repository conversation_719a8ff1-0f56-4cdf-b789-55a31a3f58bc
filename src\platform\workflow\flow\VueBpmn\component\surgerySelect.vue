<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px" @close="visible = false"
    v-loading="loading">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选手术" :class="{ 'is-error': choosedSurgeryShow.length === 0 }">
          <div class="tag-container">
            <div class="tag-scroll-container">
              <el-tag v-for="(surgery, index) in choosedSurgeryShow" :key="index" closable
                @close="handleRemoveTag(index)" style="margin: 2px 5px 2px 0;">
                {{ surgery }}
              </el-tag>
            </div>
            <div v-if="choosedSurgeryShow.length === 0" class="el-form-item__error">
              请选择手术
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入手术">
          <el-input placeholder="输入关键字过滤手术" v-model="filterTextSurgery" size="small" clearable
            @clear="dataSurgery = []">
            <i @click="initLeftSurgeryTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataSurgery" show-checkbox node-key="id" :props="defaultProps"
            style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeSurgery" ref="treeSurgery"
            :expand-on-click-node="false" :highlight-current="true" @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
      }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
/**
 * @name surgery-select (组件名称)
 * @module 组件存放位置
 * @desc 组件描述
 * <AUTHOR>
 * @param {Object} [title]    - 参数说明
 * @param {String} [columns] - 参数说明
 * @example 调用示例
 *  <KS-Form></KS-Form>
 */
import * as api from '../../api'

export default {
  name: 'surgery-select',
  components: {},
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      title: '选择手术',
      filterTextSurgery: '',
      dataSurgery: [],
      defaultProps: {
        children: 'children',
        label: 'surgeryName'
      },
      // 本地已选的手术（通过props传入，不可通过树操作修改）
      localSelectedSurgeries: [],
      // 新选择的手术（通过树操作添加/删除）
      newSelectedSurgeries: []
    }
  },
  mounted() {
  },
  methods: {
    init(data) {
      this.visible = true
      this.newSelectedSurgeries = []
      this.filterTextSurgery = ''
      this.dataSurgery = []

      // 初始化本地已选择的手术
      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')  // 编码用逗号分割
        const names = this.selectedName.split(';')  // 名称用分号分割

        this.localSelectedSurgeries = codes.map((code, index) => ({
          operationCode: code,
          surgeryName: names[index],
          isLocal: true // 标记为本地已选项目
        }))
      } else {
        this.localSelectedSurgeries = []
      }

      this.$nextTick(() => {
        this.initLeftSurgeryTreeData()
      })
    },
    initData(data) {
      this.$refs.treeSurgery.setCheckedKeys(data.split(','))  // 编码用逗号分割
    },
    getParams() {
      return this.newSelectedSurgeries.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, () => false)
    },
    handleCheckChange() {
      // 延迟执行，让树的状态先更新
      this.$nextTick(() => {
        this.syncTreeSelection();
      });
    },

    // 同步树的选择状态到数据
    syncTreeSelection() {
      if (!this.$refs.treeSurgery) return;

      // 获取当前树中所有选中的叶子节点
      const checkedNodes = this.$refs.treeSurgery.getCheckedNodes();
      const checkedLeafNodes = checkedNodes.filter(node =>
        !node.children || node.children.length === 0
      );

      // 获取当前选中的手术编码
      const checkedCodes = checkedLeafNodes.map(node => node.operationCode);

      // 更新本地已选列表：移除未选中的项目
      this.localSelectedSurgeries = this.localSelectedSurgeries.filter(item =>
        checkedCodes.includes(item.operationCode)
      );

      // 重新构建新选择列表：只包含不在本地已选中的项目
      this.newSelectedSurgeries = checkedLeafNodes
        .filter(node => !this.isInLocalSelected(node.operationCode))
        .map(node => ({
          id: node.id,
          operationCode: node.operationCode,
          surgeryName: node.surgeryName,
          isLocal: false
        }));
    },

    // 检查是否在本地已选中
    isInLocalSelected(operationCode) {
      return this.localSelectedSurgeries.some(item =>
        item.operationCode === operationCode
      );
    },
    initLeftSurgeryTreeData() {
      if (this.filterTextSurgery) {
        this.loading = true;
        api.surgeryList(this.filterTextSurgery).then((res) => {
          this.dataSurgery = res;

          // 设置本地已选项目的选中状态
          this.setInitialCheckedState();

          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      }
    },

    // 设置初始选中状态
    setInitialCheckedState() {
      if (!this.$refs.treeSurgery) return;

      const selectedCodes = this.localSelectedSurgeries.map(item => item.operationCode);
      const checkedIds = [];

      const traverse = (nodes) => {
        for (const node of nodes) {
          // 如果是叶子节点且在本地已选中，则标记为选中
          if ((!node.children || node.children.length === 0) &&
              selectedCodes.includes(node.operationCode)) {
            checkedIds.push(node.id);
          }

          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        }
      };

      traverse(this.dataSurgery);
      this.$refs.treeSurgery.setCheckedKeys(checkedIds);
    },
    filterNodeSurgery(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleRemoveTag(index) {
      const totalLocal = this.localSelectedSurgeries.length;

      if (index < totalLocal) {
        // 删除本地已选项目
        const removedItem = this.localSelectedSurgeries[index];
        this.localSelectedSurgeries.splice(index, 1);

        // 取消树中对应节点的选中状态
        this.uncheckTreeNodesByCode(removedItem.operationCode);
      } else {
        // 删除新选择的项目
        const newIndex = index - totalLocal;
        const removedItem = this.newSelectedSurgeries[newIndex];

        // 取消树中对应节点的选中状态
        if (this.$refs.treeSurgery && removedItem) {
          this.$refs.treeSurgery.setChecked(removedItem.id, false);
        }
      }

      // 统一触发同步，确保状态一致
      this.$nextTick(() => {
        this.syncTreeSelection();
      });
    },

    // 根据手术编码取消选中树节点
    uncheckTreeNodesByCode(operationCode) {
      if (!this.$refs.treeSurgery) return;

      const matchingNodes = this.findAllNodesByOperationCode(this.dataSurgery, operationCode);
      matchingNodes.forEach(node => {
        // 只取消选中叶子节点
        if (!node.children || node.children.length === 0) {
          this.$refs.treeSurgery.setChecked(node.id, false);
        }
      });
    },
    findNodeByOperationCode(nodes, operationCode) {
      for (const node of nodes) {
        if (node.operationCode === operationCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByOperationCode(node.children, operationCode)
          if (found) return found
        }
      }
      return null
    },

    // 查找所有匹配operationCode的节点
    findAllNodesByOperationCode(nodes, operationCode) {
      const matchingNodes = []
      const traverse = (nodeList) => {
        for (const node of nodeList) {
          if (node.operationCode === operationCode) {
            matchingNodes.push(node)
          }
          if (node.children && node.children.length) {
            traverse(node.children)
          }
        }
      }
      traverse(nodes)
      return matchingNodes
    },
    async dataFormSubmitHandle() {
      if (this.allSelectedSurgeries.length === 0) {
        this.$message.error('请选择手术')
        return
      }

      this.loading = true
      try {
        const finalOperationCode = this.allSelectedSurgeries.map(item => item.operationCode).join(',')  // 编码用逗号拼接
        const finalSurgeryName = this.allSelectedSurgeries.map(item => item.surgeryName).join(';')      // 名称用分号拼接

        this.$emit('callback', {
          operationCode: finalOperationCode,
          surgeryName: finalSurgeryName
        })
        this.visible = false
      } catch (err) {
        console.error('提交失败:', err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    // 所有已选择的手术（本地已选 + 新选择）
    allSelectedSurgeries() {
      return [...this.localSelectedSurgeries, ...this.newSelectedSurgeries];
    },

    // 用于显示的手术名称列表
    choosedSurgeryShow() {
      return this.allSelectedSurgeries.map(item => item.surgeryName);
    },

    // 用于兼容的手术编码
    choosedSurgeryCode() {
      return this.allSelectedSurgeries.map(item => item.operationCode).join(',');  // 编码用逗号拼接
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  margin-bottom: 5px;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}

.tag-container-wrapper {
  position: relative;
}

.tag-scroll-container {
  max-height: 120px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.tag-scroll-container .el-tag {
  margin: 4px 6px 4px 0;
}
</style>