<!-- 药品管理 -->
<template>
  <d2-container class="page">
    <el-form :inline="true" @keyup.enter.native="getDataList()" size="small" ref="dataForm" :model="dataForm">
      <el-form-item label="药品名称：">
        <el-input clearable placeholder="请输入药品名称" v-model="dataForm.drugName" @input="validateDrugName"
          @blur="validateDrugName">
        </el-input>
      </el-form-item>
      <el-form-item label="分类：">
        <el-input clearable placeholder="请输入分类" v-model="dataForm.inventoryType"></el-input>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataForm.status" placeholder="请选择状态" clearable size="small">
          <el-option v-for="(item, index) in statusList" :label="item.name"  :value="item.id" :key="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用药方式：">
        <el-input clearable placeholder="请输入用药方式" v-model="dataForm.drugUsage"></el-input>
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="dataForm.drugType" placeholder="请选择类型" clearable size="small">
          <el-option v-for="(item, index) in drugTypeList" :label="item.name"  :value="item.id" :key="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="药品通用名：">
        <el-input clearable placeholder="请输入药品通用名" v-model="dataForm.recipeGenericName"></el-input>
      </el-form-item>
      <br>
      <el-form-item>
        <el-button @click="getDataList('cx')" type="primary">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="resetHandle()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="deleteHandle()" type="danger">批量删除
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-upload class="upload-demo" action="" :http-request="uploadFile" :show-file-list="false"
          :before-upload="beforeUpload">
          <el-button type="success">文件上传</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button @click="exportHandle()" type="success">导出
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-popover placement="bottom" width="200" trigger="click">
          <el-checkbox-group v-model="selectedColumns" @change="updateTableColumns">
            <el-checkbox v-for="(item, index) in columnsOptions" :key="index" :label="item.prop"
              style="display: block;">
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
          <el-button slot="reference" type="primary">
            筛选表头 <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
        </el-popover>
      </el-form-item>
    </el-form>
    <el-table ref="listTable" :data="dataList" @selection-change="dataListSelectionChangeHandle"
      @sort-change="dataListSortChangeHandle" stripe highlight-current-row :height="heightY - topY + 'px'" border
      style="width: 100%;margin-top: -10px" size="small" v-loading="dataListLoading">
      <el-table-column align="center" header-align="center" type="selection" width="50"></el-table-column>
      <el-table-column v-for="column in filteredColumns" :key="column.prop" :prop="column.prop" :label="column.name" :width="column.width"
        align="center" header-align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag
            v-if="column.prop === 'countryCentralizedProcurementDesc' || column.prop === 'isCountryBasicDrugDesc' || column.prop === 'isProvinceBasicDrugDesc'"
            :type="scope.row[column.prop] === '否' ? 'danger' : ''" size="small">
            {{ scope.row[column.prop] }}
          </el-tag>
          <el-tag v-else-if="column.prop === 'status'" :type="scope.row.status == 1 ? 'danger' : 'primary'"
            size="small">
            {{ scope.row.status == 1 ? "停用" : "正常" }}
          </el-tag>
          <span v-else>{{ scope.row[column.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" header-align="center" label="操作">
        <template slot-scope="scope">
          <el-button @click="deleteHandle(scope.row.id)" size="small" type="text" style="color: #f56c6c;">删除</el-button>
          <el-button @click="openDetailsDrawer(scope.row.id)" size="small" type="text">查看
          </el-button>
          <el-button @click="openUsageDrawer(scope.row)" size="small" type="text">用法用量配置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right;">
      <el-pagination style="margin-top: 20px" background :current-page="page" :page-size="limit"
        :page-sizes="[10, 20, 50, 100]" :total="total" @current-change="pageCurrentChangeHandle"
        @size-change="pageSizeChangeHandle" layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </div>
    <!-- 查看详情抽屉子组件 -->
    <detailDrawer v-model="detailsDrawerVisible" ref="detailDrawer"></detailDrawer>
    <!-- 用法用量配置抽屉子组件 -->
    <usageDosageConfigurationDrawer v-model="usageDrawerVisible" @refreshDataList="getDataList" ref="usageDrawer">
    </usageDosageConfigurationDrawer>
  </d2-container>
</template>

<script>
import * as api from './api'
import util from '@/libs/util'
import detailDrawer from './components/details.vue' // 查看详情
import usageDosageConfigurationDrawer from './components/usage-dosage-configuration.vue' // 用法用量配置
import resizemix from '@/components/qz-mixins/windowresize/resizemix'
import { clone } from 'lodash'
export default {
  name: 'sys-role',
  mixins: [resizemix],
  components: {
    detailDrawer,
    usageDosageConfigurationDrawer
  },
  data() {
    return {
      heightY: document.body.clientHeight - 100,
      topY: 210,
      dataForm: {
        drugName: '', // 药品名称
        inventoryType: '', // 分类
        status: '', // 状态
        recipeGenericName: '', // 药品通用名
        drugUsage: '', // 用药方式
        drugType: '' // 类型
      },
      statusList: [ // 状态列表
        // { id: '', name: '全部' },
        { id: 0, name: '正常' },
        { id: 1, name: '停用' }
      ],
      drugTypeList: [ // 类型列表
        { id: '', name: '全部' },
        { id: 0, name: '基础用药' },
        { id: 1, name: '医保用药' }
      ],
      dataList: [],
      order: '', // 排序，asc／desc
      orderField: '', // 排序，字段
      page: 1, // 当前页码
      limit: 10, // 每页数 limit 不会与字段重复
      total: 0, // 总条数
      dataListLoading: false,
      searchShow: false,
      dataListSelections: [], // 数据列表，多选项
      detailsDrawerVisible: false, // 详情抽屉子组件
      usageDrawerVisible: false, // 用法用量配置抽屉子组件
      // 表头选项
      columnsOptions: [
        { name: '名称', prop: 'drugName',width:'300' },
        { name: '规格', prop: 'drugSpecification',width:'120' },
        { name: '生产企业', prop: 'producingEnterprise',width:'100' },
        { name: '价格', prop: 'purchasePrice',width:'70' }, // 对应进价(入库单位)
        { name: '单位', prop: 'basicUnit',width:'60' },
        { name: '剂型', prop: 'dosageForm',width:'60' },
        { name: '商品名', prop: 'recipeGenericName',width:'120' }, // 对应药品通用名
        { name: '药学分类', prop: 'pharmacyType',width:'200' },
        { name: '医保编码', prop: 'medicalInsuranceCoding',width:'100' },
        { name: '国家集中采购', prop: 'countryCentralizedProcurementDesc',width:'94' },
        { name: '国家基本药物', prop: 'isCountryBasicDrugDesc',width:'94' },
        { name: '省基本药物', prop: 'isProvinceBasicDrugDesc',width:'94' },
        { name: '医保用药', prop: 'medicalInsuranceCategory',width:'80' }, // 对应医保类别 为空时显示空
        { name: '状态', prop: 'status',width:'80' }
      ],
      // 选中的表头（默认选中所有）
      selectedColumns: ['drugName', 'drugSpecification', 'producingEnterprise', 'purchasePrice', 'basicUnit', 'dosageForm', 'recipeGenericName', 'pharmacyType', 'medicalInsuranceCoding', 'countryCentralizedProcurementDesc', 'isCountryBasicDrugDesc', 'isProvinceBasicDrugDesc', 'medicalInsuranceCategory', 'status']
    }
  },
  mounted() {
    this.getDataList()
  },
  computed: {
    // 过滤后的表头
    filteredColumns() {
      return this.columnsOptions.filter(col => this.selectedColumns.includes(col.prop))
    }
  },
  methods: {
    updateTableColumns() {
      // 选中项变化时，更新表格列
      // this.filteredColumns
      this.$refs.listTable.doLayout()
    },
    // 分页
    getDataList(val) {
      // 在发送请求前进行校验
      if (this.dataForm.drugName && /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]/.test(this.dataForm.drugName)) {
        this.$message.error('药品名称包含特殊符号，无法查询')
        return
      }

      if (val === 'cx') {
        this.page = 1
      }
      const params = {
        order: this.order,
        orderField: this.orderField,
        page: this.page,
        limit: this.limit,
        ...this.dataForm
      }
      this.dataListLoading = true
      api.drugPage(params).then((data) => {
        this.dataList = data.records
        this.total = data.total * 1
      }).catch((err) => {
        this.dataListLoading = false
        util.$message.showInfo2(err)
      })
      this.dataListLoading = false
      this.$nextTick(() => {
        // 解决固定列后错位的问题
        this.$refs.listTable.doLayout()
      })
    },
    // 重置
    resetHandle() {
      this.dataForm = this.$options.data().dataForm
      this.page = 1
      this.limit = 10
      this.getDataList()
    },
    // 查看
    openDetailsDrawer(id) {
      this.detailsDrawerVisible = true
      this.$nextTick(() => {
        this.$refs.detailDrawer.init(id)
      })
    },
    // 用法用量配置
    openUsageDrawer(data) {
      this.usageDrawerVisible = true
      this.$nextTick(() => {
        this.$refs.usageDrawer.init(data)
      })
    },
    dataListSelectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 排序
    dataListSortChangeHandle(data) {
      if (!data.order || !data.prop) {
        this.order = ''
        this.orderField = ''
        return false
      }
      this.order = data.order
      this.orderField = data.prop
      this.getDataList()
    },
    async deleteHandle(id) {
      if (!id && this.dataListSelections.length <= 0) {
        return util.$message.showInfo2('请选择需要删除的项')
      }
      const ok = await util.$message.showYesNo('是否确定删除选择的数据?')
      if (!(ok && ok === 'confirm')) {
        return ''
      }
      const ids = id ? [id] : this.dataListSelections.map(item => item.id)
      console.log(ids)
      await api.drugDelete(ids)
      this.getDataList()
    },
    // 分页, 每页条数
    pageSizeChangeHandle(val) {
      this.page = 1
      this.limit = val
      this.getDataList()
    },
    // 分页, 当前页
    pageCurrentChangeHandle(val) {
      this.page = val
      this.getDataList()
    },
    // 导出
    exportHandle() {
      api.exportXls(this.dataForm)
    },
    // 文件上传
    // 校验文件类型
    beforeUpload(file) {
      const isExcel =
        file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel'
      if (!isExcel) {
        this.$message.error('只能上传 Excel 文件 (.xls, .xlsx)')
      }
      return isExcel // 返回 false 阻止上传
    },

    // 自定义上传方法
    async uploadFile(params) {
      const formData = new FormData()
      formData.append('file', params.file)
      console.log(params)
      this.dataListLoading = true
      api.uploadExcel(formData)
        .then((res) => {
          this.$message.success('上传成功')
          this.dataListLoading = false
          this.getDataList()
        })
        .catch((err) => {
          console.error('上传出错:', err)
          this.$message.error('上传失败，请稍后重试')
          this.dataListLoading = false
        })
    },
    validateDrugName() {
      if (!this.dataForm.drugName) return

      // 定义特殊符号正则表达式
      const specialCharsRegex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]/

      if (specialCharsRegex.test(this.dataForm.drugName)) {
        this.$message.warning('药品名称不能包含特殊符号')
        // 移除特殊符号
        this.dataForm.drugName = this.dataForm.drugName.replace(specialCharsRegex, '')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.page {
  .el-radio {
    margin-right: 10px;
  }
  .el-table {
  table-layout: fixed;
}

  // .el-input, .el-select {
  //   width: 100%;
  // }
}
</style>
